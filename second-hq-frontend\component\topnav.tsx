"use client";
import { cn } from "@/lib/cn";

type TopNavProps = {
  onToggleSidebar: () => void;
};

export default function TopNav({ onToggleSidebar }: TopNavProps) {
  return (
    <header className="sticky top-0 z-30 border-b border-gray-200 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="flex h-14 items-center gap-3 px-4">
        {/* Mobile hamburger */}
        <button
          onClick={onToggleSidebar}
          className="lg:hidden inline-flex items-center justify-center rounded-xl border px-2.5 py-2 text-sm"
          aria-label="Toggle sidebar"
        >
          ☰
        </button>

        {/* Brand (desktop) */}
        <div className="hidden lg:block text-sm text-gray-600">
          <strong>MyApp</strong> • Dashboard
        </div>

        {/* Search */}
        <div className="ml-auto flex items-center gap-3">
          <form
            onSubmit={(e) => e.preventDefault()}
            className="hidden md:flex items-center gap-2 rounded-xl border px-3 py-2"
            role="search"
          >
            <input
              placeholder="Search…"
              className="w-56 outline-none text-sm bg-transparent placeholder:text-gray-400"
            />
            <kbd className="rounded border px-1.5 text-xs text-gray-500">/</kbd>
          </form>

          {/* Simple user dropdown using <details> for zero JS */}
          <details className="relative">
            <summary className="list-none cursor-pointer rounded-full border px-3 py-1 text-sm">
              User ▾
            </summary>
            <div className="absolute right-0 mt-2 w-40 overflow-hidden rounded-xl border bg-white shadow">
              <a className="block px-3 py-2 text-sm hover:bg-gray-50" href="/profile">Profile</a>
              <a className="block px-3 py-2 text-sm hover:bg-gray-50" href="/billing">Billing</a>
              <button className="block w-full text-left px-3 py-2 text-sm hover:bg-gray-50">Log out</button>
            </div>
          </details>
        </div>
      </div>
    </header>
  );
}
