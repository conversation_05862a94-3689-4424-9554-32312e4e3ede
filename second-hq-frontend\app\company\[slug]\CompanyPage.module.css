.pageContainer {
  min-height: 100vh;
  background: #ffffff;
  padding-top: 72px; /* Account for navbar */
}

.header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 2rem 0;
}

.headerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.companyInfo {
  display: flex;
  gap: 1.5rem;
  flex: 1;
}

.logoContainer {
  width: 80px;
  height: 80px;
  background: #8b5cf6;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo {
  font-size: 2rem;
  color: white;
}

.companyDetails {
  flex: 1;
}

.companyName {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.companyDescription {
  color: #1f2937; /* darker for readability */
  font-size: 1rem;
  line-height: 1.65;
  margin: 0 0 1rem 0;
}

.tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #374151;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid #d1d5db;
}

.headerActions {
  flex-shrink: 0;
}

.visitButton {
  background: #16d1aa;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.visitButton:hover {
  background: #14b8a6;
  transform: translateY(-1px);
}

.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem;
  overflow-x: hidden;
  position: relative;
}

/* New two-column layout with sticky sidebar */
.layoutContainer {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 2rem;
  align-items: start;
}

.sidebar {
  position: sticky;
  top: 2rem;
  height: fit-content;
  z-index: 10;
}

.sidebarContent {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.25rem;
  overflow: hidden;
}

.sidebarTitle {
  font-size: 0.95rem;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 0.75rem 0;
}

.sidebarItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem 0;
  border-bottom: 1px solid #f1f5f9;
}
.sidebarItem:last-child { border-bottom: none; }

.sidebarLabel {
  color: #64748b;
  font-size: 0.85rem;
}

.sidebarValue {
  color: #0f172a;
  font-weight: 600;
  font-size: 0.9rem;
}

.sidebarLink {
  color: #16d1aa;
  text-decoration: none;
  font-weight: 600;
}
.sidebarLink:hover { color: #14b8a6; }

.contentArea {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  min-height: 100vh;
  overflow-y: visible;
}

/* Old grid removed since snapshots are stacked now */

.snapshotCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metricsCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  grid-column: span 2;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.75rem 0;
  border-bottom: 2px solid #16d1aa;
  padding-bottom: 0.5rem;
}

.sectionSubtitle {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0 0 1rem 0;
}

.snapshotContent {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.snapshotItem {
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
  line-height: 1.6;
  color: #0f172a; /* darker body text */
}

.snapshotItem:last-child {
  border-bottom: none;
}

.snapshotItem strong {
  color: #0f172a; /* darker label */
  font-weight: 700;
  display: inline-block;
  min-width: 160px;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.metricItem {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border: 1px solid #e2e8f0;
}

.metricLabel {
  display: block;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.metricValue {
  display: block;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 700;
}

.aboutSection {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.aboutText {
  color: #0f172a;
  line-height: 1.75;
  font-size: 1rem;
}

.aboutContent p {
  color: #0f172a; /* darker paragraphs */
  line-height: 1.75;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .headerContent {
    flex-direction: column;
    gap: 1.5rem;
  }

  .contentGrid {
    grid-template-columns: 1fr;
  }

  .metricsCard {
    grid-column: span 1;
  }

  .mainContent {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 768px) {
  .companyInfo {
    flex-direction: column;
    gap: 1rem;
  }

  .logoContainer {
    width: 60px;
    height: 60px;
  }

  .logo {
    font-size: 1.5rem;
  }

  .companyName {
    font-size: 1.5rem;
  }

  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .snapshotCard,
  .metricsCard,
  .aboutSection {
    padding: 1.5rem;
  }
}
