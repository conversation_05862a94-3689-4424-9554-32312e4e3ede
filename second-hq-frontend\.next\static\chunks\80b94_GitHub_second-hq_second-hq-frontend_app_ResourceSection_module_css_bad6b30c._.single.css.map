{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/Documents/GitHub/second-hq/second-hq-frontend/app/ResourceSection.module.css"], "sourcesContent": [".sectionWrapper {\r\n  display: flex;\r\n  background: #f9fafb;\r\n  padding: 3rem 2rem;\r\n  min-height: 90vh;\r\n  gap: 3rem;\r\n  max-width: 1320px;\r\n  margin: 0 auto;\r\n  width: 100%;\r\n}\r\n\r\n.sidebar {\r\n  min-width: 280px;\r\n  background: #ffffff;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 6rem;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.sidebarButton {\r\n  background: transparent;\r\n  border: none;\r\n  color: #6b7280;\r\n  font-size: 0.9rem;\r\n  border-radius: 6px;\r\n  padding: 0.75rem 1rem;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  transition: all 0.2s ease;\r\n  font-weight: 500;\r\n  position: relative;\r\n}\r\n\r\n.sidebarButton.active,\r\n.sidebarButton:hover {\r\n  background: #f3f4f6;\r\n  color: #16d1aa;\r\n  transform: none;\r\n}\r\n\r\n.sidebarCount {\r\n  background: #16d1aa;\r\n  color: #ffffff;\r\n  border-radius: 6px;\r\n  padding: 0.25rem 0.5rem;\r\n  margin-left: auto;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.sidebarCategory {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  color: #6b7280;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  padding: 0.75rem 1rem;\r\n  border-radius: 6px;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.sidebarCategory:hover {\r\n  background: #f3f4f6;\r\n  color: #16d1aa;\r\n}\r\n\r\n.grid {\r\n  flex: 1;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n  gap: 1.5rem;\r\n}\r\n\r\n.card {\r\n  background: #ffffff;\r\n  border-radius: 8px;\r\n  padding: 1.5rem;\r\n  border: 1px solid #e5e7eb;\r\n  color: #374151;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.2s ease;\r\n  position: relative;\r\n  cursor: pointer;\r\n  min-height: 200px;\r\n}\r\n\r\n.card:hover {\r\n  border: 1px solid #16d1aa;\r\n  box-shadow: 0 4px 12px rgba(22, 209, 170, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.cardIcon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 8px;\r\n  margin-bottom: 1rem;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 1.5rem;\r\n  background: #f3f4f6;\r\n  color: #374151;\r\n}\r\n\r\n.cardTitle {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  margin-bottom: 0.75rem;\r\n  color: #111827;\r\n  line-height: 1.4;\r\n}\r\n\r\n.cardDesc {\r\n  font-size: 0.95rem;\r\n  color: #6b7280;\r\n  margin-bottom: 1rem;\r\n  line-height: 1.6;\r\n}\r\n\r\n.cardLink {\r\n  position: absolute;\r\n  top: 1.5rem;\r\n  right: 1.5rem;\r\n  color: #9ca3af;\r\n  font-size: 1.2rem;\r\n  text-decoration: none;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.cardLink:hover {\r\n  color: #16d1aa;\r\n  transform: none;\r\n}\r\n\r\n/* Add category badge styling */\r\n.categoryBadge {\r\n  display: inline-block;\r\n  background: #f3f4f6;\r\n  color: #6b7280;\r\n  padding: 0.25rem 0.75rem;\r\n  border-radius: 6px;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  margin-top: 0.5rem;\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n/* Responsive design */\r\n@media (max-width: 1024px) {\r\n  .sectionWrapper {\r\n    flex-direction: column;\r\n    padding: 2rem 1.5rem;\r\n    gap: 2rem;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .sidebar {\r\n    min-width: unset;\r\n    position: static;\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .sectionWrapper {\r\n    padding: 1.5rem 1rem;\r\n    gap: 1.5rem;\r\n  }\r\n\r\n  .sidebar {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .card {\r\n    padding: 1.5rem;\r\n    min-height: 180px;\r\n  }\r\n}\r\n\r\n/* Add subtle animations */\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.card {\r\n  animation: fadeInUp 0.6s ease forwards;\r\n}\r\n\r\n.card:nth-child(1) { animation-delay: 0.1s; }\r\n.card:nth-child(2) { animation-delay: 0.2s; }\r\n.card:nth-child(3) { animation-delay: 0.3s; }\r\n.card:nth-child(4) { animation-delay: 0.4s; }\r\n.card:nth-child(5) { animation-delay: 0.5s; }\r\n.card:nth-child(6) { animation-delay: 0.6s; }\r\n\r\n/* Dropdown styles */\r\n.subDropdown {\r\n  margin-top: 0.5rem;\r\n  margin-left: 1rem;\r\n  border-left: 2px solid rgba(29, 233, 182, 0.2);\r\n  padding-left: 1rem;\r\n}\r\n\r\n.subDropdownItem {\r\n  padding: 0.75rem 1rem;\r\n  color: #94a3b8;\r\n  cursor: pointer;\r\n  border-radius: 8px;\r\n  font-size: 0.9rem;\r\n  transition: all 0.2s ease;\r\n  margin-bottom: 0.25rem;\r\n}\r\n\r\n.subDropdownItem:hover,\r\n.subDropdownItem.active {\r\n  background: rgba(29, 233, 182, 0.1);\r\n  color: #1de9b6;\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA;;;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;AAiBA;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;;;;;;AAaA;EACE;;;;;;;EAOA;;;;;;EAMA;;;;;;AAMF;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;AAOF;;;;;;;;;;;;AAWA;;;;AAIA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AACA;;;;AAGA;;;;;;;AAOA;;;;;;;;;;AAUA"}}]}