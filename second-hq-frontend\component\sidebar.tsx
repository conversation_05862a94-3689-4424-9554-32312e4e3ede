"use client";
import { cn } from "@/lib/cn";
import NavLink from "./navbar";
import { usePathname } from "next/navigation";
import React from "react";

type SidebarProps = {
  open: boolean;
  onClose: () => void;
};

const links = [
  { href: "/", label: "Dashboard", exact: true },
  { href: "/projects", label: "Projects" },
  { href: "/team", label: "Team" },
  { href: "/settings", label: "Settings" },
];

export default function Sidebar({ open, onClose }: SidebarProps) {
  const pathname = usePathname();

  // Close sidebar on route change (mobile)
  React.useEffect(() => {
    onClose();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  return (
    <>
      {/* Backdrop on mobile */}
      <div
        className={cn(
          "fixed inset-0 z-40 bg-black/30 lg:hidden transition-opacity",
          open ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
        )}
        aria-hidden={!open}
        onClick={onClose}
      />

      {/* Drawer / Static sidebar */}
      <aside
        className={cn(
          "fixed z-50 inset-y-0 left-0 w-72 bg-white border-r border-gray-200 p-4 lg:static lg:translate-x-0 lg:inset-auto lg:z-auto",
          "transition-transform will-change-transform",
          open ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
        aria-label="Sidebar"
      >
        <div className="mb-4">
          <span className="text-lg font-semibold">MyApp</span>
          <span className="ml-2 rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-600">
            v1.0
          </span>
        </div>

        <nav className="space-y-1">
          {links.map((l) => (
            <NavLink
              key={l.href}
              href={l.href}
              exact={l.exact}
              onClick={onClose}
            >
              {l.label}
            </NavLink>
          ))}
        </nav>

        <div className="mt-6 border-t pt-4">
          <div className="text-xs uppercase text-gray-400 mb-2">Shortcuts</div>
          <div className="flex flex-wrap gap-2">
            <a className="text-xs rounded-full border px-2 py-1 text-gray-600 hover:bg-gray-50" href="#">
              + New
            </a>
            <a className="text-xs rounded-full border px-2 py-1 text-gray-600 hover:bg-gray-50" href="#">
              Import
            </a>
          </div>
        </div>
      </aside>
    </>
  );
}
