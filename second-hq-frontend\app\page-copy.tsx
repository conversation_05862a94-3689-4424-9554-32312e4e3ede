"use client";

import { useState } from "react";

const navLinks = [
    { name: "Home", href: "#" },
    { name: "Resources", href: "#" },
    { name: "Directory", href: "#" },
    { name: "Blog", href: "#" },
    { name: "Style", href: "#" },
    { name: "Members", href: "#" },
    { name: "Tag", href: "#" },
    { name: "Author", href: "#" },
];

function TopNavbar() {
    return (
        <nav style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            padding: "1rem 2rem",
            background: "#fff",
            borderBottom: "1px solid #e5e7eb",
            position: "sticky",
            top: 0,
            zIndex: 100
        }}>
            <div style={{
                fontSize: "1.5rem",
                fontWeight: 700,
                color: "#16d1aa"
            }}>
                Rinne
            </div>
            <div style={{
                display: "flex",
                gap: "2rem",
                alignItems: "center"
            }}>
                {navLinks.map((link) => (
                    <a
                        key={link.name}
                        href={link.href}
                        style={{
                            textDecoration: "none",
                            color: link.name === "Home" ? "#16d1aa" : "#374151",
                            fontWeight: link.name === "Home" ? 600 : 500,
                            fontSize: "0.95rem",
                            transition: "color 0.2s ease"
                        }}
                    >
                        {link.name}
                    </a>
                ))}
            </div>
            <button style={{
                background: "#16d1aa",
                color: "#fff",
                border: "none",
                borderRadius: "6px",
                padding: "0.75rem 1.5rem",
                fontWeight: 600,
                fontSize: "0.9rem",
                cursor: "pointer",
                transition: "background 0.2s ease"
            }}>
                Subscribe
            </button>
        </nav>
    );
}

const categories = [
  { name: "All resources", icon: "📦" },
  { name: "Analytics", icon: "📊", sub: ["Google Analytics", "Mixpanel", "Fathom", "Plausible", "Simple Analytics"] },
  { name: "Automation", icon: "⚙️", sub: ["Zapier", "Make", "Clay"] },
  { name: "Data", icon: "💾", sub: ["Airtable", "Notion", "Stacker"] },
  { name: "Design", icon: "🎨", sub: ["Figma", "Canva", "Framer"] },
  { name: "Logo", icon: "🪧", sub: ["Logology", "Canva"] },
  { name: "Membership", icon: "👥", sub: ["Lemon Squeezy", "Gumroad", "Patreon"] },
  { name: "Newsletter", icon: "📧", sub: ["Ghost", "Revue", "Substack"] },
  { name: "Productivity", icon: "⚡", sub: ["Notion", "Basecamp", "Trello"] },
  { name: "SEO", icon: "🔍", sub: ["Ahrefs", "Semrush"] },
  { name: "Website", icon: "🌐", sub: ["Framer", "Webflow", "Ghost"] },
  { name: "Payment", icon: "💳", sub: ["Lemon Squeezy", "Gumroad"] },
];

type Resource = {
  title: string;
  desc: string;
  icon: string;
  category: string;
  link: string;
  tags?: string[];
};

const resources: Resource[] = [
  {
    title: "Lemon Squeezy",
    desc: "Lemon Squeezy is the all-in-one platform for running your SaaS business. Payments, subscriptions, global tax compliance, fraud prevention, and more.",
    icon: "🍋",
    category: "Analytics",
    link: "#",
    tags: ["Paid", "Free"],
  },
  {
    title: "Ghost",
    desc: "The world's most popular modern publishing platform for creating a new media platform.",
    icon: "🟣",
    category: "Automation",
    link: "#",
    tags: ["Marketing"],
  },
  {
    title: "Figma",
    desc: "Build better products as a team. Design, prototype, and gather feedback all in one place.",
    icon: "🎨",
    category: "Design",
    link: "#",
    tags: ["Product"],
  },
  // Add more resources as needed
];

function ResourceSection() {
  const [selected, setSelected] = useState("All resources");
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [selectedSub, setSelectedSub] = useState<string | null>(null);

  const filteredResources =
    selected === "All resources"
      ? resources
      : selectedSub
      ? resources.filter((r) => r.category === selected && r.tags?.includes(selectedSub))
      : resources.filter((r) => r.category === selected);

  return (
    <div className={resourceStyles.sectionWrapper}>
      <aside className={resourceStyles.sidebar}>
        {categories.map((cat) => (
          <div key={cat.name}>
            <button
              className={`${resourceStyles.sidebarButton} ${
                selected === cat.name ? resourceStyles.active : ""
              }`}
              onClick={() => {
                setSelected(cat.name);
                setSelectedSub(null);
                // Only open dropdown if not "All resources"
                if (cat.name !== "All resources") {
                  setOpenDropdown(openDropdown === cat.name ? null : cat.name);
                }
              }}
              style={{
                color: selected === cat.name ? "#1de9b6" : undefined,
                fontWeight: selected === cat.name ? 600 : undefined,
                justifyContent: "flex-start",
                width: "100%",
              }}
            >
              <span>{cat.icon}</span>
              {cat.name}
              {cat.name === "All resources" && (
                <span className={resourceStyles.sidebarCount}>{resources.length}</span>
              )}
              {/* Arrow only for categories except All resources */}
              {cat.name !== "All resources" && (
                <span style={{ marginLeft: "auto", fontSize: 18 }}>
                  {openDropdown === cat.name ? "▲" : "▼"}
                </span>
              )}
            </button>
            {/* Dropdown only for categories except All resources */}
            {cat.name !== "All resources" && openDropdown === cat.name && (
              <div className={resourceStyles.subDropdown}>
                {cat.sub && cat.sub.length > 0 ? (
                  cat.sub.map((sub) => (
                    <div
                      key={sub}
                      className={`${resourceStyles.subDropdownItem} ${
                        selectedSub === sub ? resourceStyles.active : ""
                      }`}
                      onClick={() => {
                        setSelected(cat.name);
                        setSelectedSub(sub);
                      }}
                    >
                      {sub}
                    </div>
                  ))
                ) : (
                  <div
                    className={resourceStyles.subDropdownItem}
                    style={{ color: "#888", fontStyle: "italic" }}
                  >
                    No subcategories
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </aside>
      <div className={resourceStyles.grid}>
        {filteredResources.map((res) => (
          <div key={res.title} className={resourceStyles.card}>
            <div className={resourceStyles.cardIcon}>{res.icon}</div>
            <div className={resourceStyles.cardTitle}>{res.title}</div>
            <div className={resourceStyles.cardDesc}>{res.desc}</div>
            <a
              href={res.link}
              className={resourceStyles.cardLink}
              target="_blank"
              rel="noopener noreferrer"
              title="Open"
            >
              ↗
            </a>
          </div>
        ))}
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <div
      style={{
        minHeight: "100vh",
        width: "100vw",
        background: "#18191b",
      }}
    >
      <div className={styles.heroSection}>
        <TopNavbar />
        <div style={{ padding: "3rem 0", textAlign: "center", margin: "12rem 0 0 0" }}>
          <h1 style={{ color: "#fff", fontWeight: 800, fontSize: "42.44px" }}>
            All the <span style={{ color: "#1de9b6" }}>best resources</span> in one place
          </h1>
          <p style={{ color: "#fff", fontFamily: "sans-serif", fontSize: "23.4px", margin: "1rem 0" }}>
            Improve & <a href="#" style={{ color: "#1de9b6", textDecoration: "underline" }}>automate</a> your work with the best no-code tools. Subscribe to get new resources weekly.
          </p>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              marginTop: "2rem",
              marginBottom: "200px"
            }}
          >
            <input
              type="email"
              placeholder="Your email address"
              style={{
                padding: "18px",
                fontSize: "1.1rem",
                border: "none",
                borderRadius: "10px 10px",
                outline: "none",
                width: "380px",
                background: "#222",
                color: "#fff",
                minHeight: "56px"
              }}
            />
            <button
              style={{
                background: "#1de9b6",
                color: "#000",
                border: "none",
                borderRadius: "0 6px 6px 0",
                padding: "0 36px",
                fontWeight: 700,
                fontSize: "1.2rem",
                width: "180px",
                cursor: "pointer",
                minHeight: "56px"
              }}
            >
              SUBSCRIBE
            </button>
          </div>
        </div>
        <svg
          width="100%"
          height="120"
          viewBox="0 0 1440 120"
          style={{
            display: "block",
            position: "absolute",
            bottom: 0,
            left: 0,
            zIndex: 2,
          }}
          preserveAspectRatio="none"
        >
          <path
            fill="#ffffff"
            d="
              M0,0
              Q720,160 1440,0
              L1440,120
              L0,120
              Z
            "
          />
        </svg>
      </div>
      <ResourceSection />
    </div>
  );
}
