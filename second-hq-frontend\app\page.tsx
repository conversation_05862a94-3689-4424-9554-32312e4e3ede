"use client";

// Force deployment update - latest version 2024
import { useState } from "react";
import Link from "next/link";
import styles from "./TopNavbar.module.css";
import resourceStyles from "./ResourceSection.module.css";

const navLinks = [
    { name: "Home", href: "#", scrollTo: "home" },
    { name: "Inside India's GCCs", href: "#", scrollTo: "all-gccs" },
    { name: "By Industry", href: "#", scrollTo: "by-industry" },
    { name: "By Function", href: "#", scrollTo: "by-function" },
    { name: "City", href: "#", scrollTo: "city" },
    { name: "AI Readiness", href: "#", scrollTo: "ai-readiness" },
    { name: "Size/Scale", href: "#", scrollTo: "size-scale" },
    { name: "Culture", href: "#", scrollTo: "culture" },
];

function TopNavbar() {
  const [activeMenu, setActiveMenu] = useState("Home");

  const onMenuClick = (menu: string, scrollTo?: string) => {
    setActiveMenu(menu);
    if (scrollTo) {
      const element = document.getElementById(scrollTo);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  return (
    <nav className={styles.navbar}>
      <div className={styles.navContainer}>
        <span
          className={styles.logo}
          style={{ cursor: "pointer" }}
          onClick={() => onMenuClick("Home")}
        >
          Second HQ
        </span>
        <div className={styles.links}>
          {navLinks.map((link) => (
            <a
              key={link.name}
              href={link.href}
              className={`${styles.navLink} ${activeMenu === link.name ? styles.active : ""}`}
              onClick={(e) => {
                e.preventDefault();
                onMenuClick(link.name, link.scrollTo);
              }}
            >
              {link.name}
            </a>
          ))}
        </div>
        <button className={styles.subscribeBtn}>Subscribe</button>
      </div>
    </nav>
  );
}


/*
const navItems = [
  { label: "Inside India’s GCCs", href: "/inside-gccs" },
  { label: "By Industry", href: "/industry" },
  { label: "By Function", href: "/function" },
  { label: "City", href: "/city" },
  { label: "AI Readiness", href: "/ai" },
  { label: "Size/Scale", href: "/scale" },
  { label: "Culture", href: "/culture" },
];
*/


const categories = [
  { name: "All GCCs", id: "all-gccs", count: 12 },
  { name: "By Industry", id: "by-industry", count: 8, sub: ["Banking & Financial\nServices (BFSI)", "Fintech", "Insurance", "Healthcare", "Pharma & MedTech", "Retail & CPG", "Energy", "Manufacturing", "Entertainment", "Travel & Hospitality", "Logistics & Supply Chain", "Enterprise SaaS", "Cloud & Infra", "Cybersecurity", "Gaming", "EdTech", "Public Sector", "Technology & IT Services", "Professional & Business Services", "Automotive & Industrial"] },
  { name: "By Function", id: "by-function", count: 6, sub: ["Engineering Hub", "Product & Design Studio", "Data/ML Center", "R&D Lab", "Risk & Compliance COE", "Finance Shared Services", "Customer Ops", "Supply Chain COE", "Security Ops Center", "Innovation Garage", "Platform/Infra Hub"] },
  { name: "City", id: "city", count: 4, sub: ["Bengaluru", "Hyderabad", "Pune", "Chennai", "Gurugram", "Noida", "Mumbai", "Ahmedabad", "Coimbatore", "Kochi", "Jaipur", "Chandigarh"] },
  { name: "AI Readiness", id: "ai-readiness", count: 3, sub: ["AI-Native", "AI Platform/CoE", "Data-Driven", "AI-Adopting", "Legacy-Modernizing", "Regulated-AI", "Early Stage-AI"] },
  { name: "Size/Scale", id: "size-scale", count: 5, sub: ["0-200 Employees", "200-500 Employees", "500-1,500 Employees", "1,500-3,000 Employees", "3,000+ Employees"] },
  { name: "Culture", id: "culture", count: 7, sub: ["Builder Culture", "Global Ownership", "High Experimentation", "Strong Learning", "Hybrid-First", "Inclusion Leader", "Low-Hierarchy", "Process-Driven", "Customer-Obsessed"] },
];

type GCC = {
  title: string;
  desc: string;
  icon: string;
  category: string;
  link: string;
  slug?: string;
  tags?: string[];
};

const gccs: GCC[] = [
  {
    title: "Microsoft India Development Center",
    desc: "One of Microsoft's largest R&D centers outside the US, focusing on cloud, AI, and enterprise solutions with over 8,000 employees.",
    icon: "🏢",
    category: "By Industry",
    link: "#",
    slug: "microsoft-india",
    tags: ["Technology & IT Services", "Hyderabad"],
  },
  {
    title: "Goldman Sachs Bengaluru",
    desc: "Major technology and operations hub for Goldman Sachs, handling global trading systems, risk management, and digital innovation.",
    icon: "🏦",
    category: "By Industry",
    link: "#",
    tags: ["Banking & Financial\nServices (BFSI)", "Bengaluru"],
  },
  {
    title: "Google India",
    desc: "Google's largest engineering center outside the US, working on products like Google Pay, Search, and Android with AI-first approach.",
    icon: "🔍",
    category: "By Function",
    link: "#",
    tags: ["Engineering Hub", "Bengaluru"],
  },
  {
    title: "Amazon Development Center",
    desc: "Amazon's largest software development center outside Seattle, focusing on AWS, Alexa, and e-commerce platforms.",
    icon: "�",
    category: "City",
    link: "#",
    tags: ["Hyderabad", "Bengaluru"],
  },
  {
    title: "Walmart Global Tech",
    desc: "Walmart's technology hub driving digital transformation with focus on e-commerce, supply chain, and data analytics.",
    icon: "�",
    category: "AI Readiness",
    link: "#",
    tags: ["AI Platform/CoE", "Bengaluru"],
  },
  {
    title: "JPMorgan Chase Technology Center",
    desc: "Global technology center focusing on digital banking, blockchain, and financial technology innovations.",
    icon: "�",
    category: "Size/Scale",
    link: "#",
    tags: ["Large (1001-5000)", "Mumbai"],
  },
  {
    title: "Accenture India",
    desc: "Leading global professional services company with technology and consulting expertise across multiple industries.",
    icon: "⚡",
    category: "By Industry",
    link: "#",
    tags: ["Technology & IT Services", "Bengaluru"],
  },
  {
    title: "Infosys Limited",
    desc: "Global leader in next-generation digital services and consulting, driving digital transformation for clients worldwide.",
    icon: "🌐",
    category: "By Function",
    link: "#",
    tags: ["Engineering Hub", "Bengaluru"],
  },
  {
    title: "TCS Innovation Labs",
    desc: "Tata Consultancy Services' research and innovation arm focusing on emerging technologies and digital solutions.",
    icon: "🔬",
    category: "AI Readiness",
    link: "#",
    tags: ["AI Platform/CoE", "Pune"],
  },
  {
    title: "Flipkart Technology",
    desc: "India's leading e-commerce platform driving innovation in retail technology, logistics, and digital payments.",
    icon: "🛒",
    category: "City",
    link: "#",
    tags: ["Bengaluru", "Technology & IT Services"],
  },
  {
    title: "Adobe India",
    desc: "Creative software giant's major development center focusing on digital media, marketing, and document solutions.",
    icon: "🎨",
    category: "Size/Scale",
    link: "#",
    tags: ["Medium (501-1000)", "Bengaluru"],
  },
  {
    title: "Intel India",
    desc: "Semiconductor leader's largest design center outside the US, working on processors, AI chips, and hardware innovations.",
    icon: "💻",
    category: "By Industry",
    link: "#",
    tags: ["Technology & IT Services", "Bengaluru"],
  },
];

// Helper functions for tag styling
function getTagClass(tag: string): string {
  if (tag.includes("Technology") || tag.includes("IT Services")) return resourceStyles.tagTechnology;
  if (tag.includes("Banking") || tag.includes("BFSI")) return resourceStyles.tagBfsi;
  if (tag.includes("Engineering")) return resourceStyles.tagEngineering;
  if (tag.includes("Bengaluru")) return resourceStyles.tagBengaluru;
  if (tag.includes("Hyderabad")) return resourceStyles.tagHyderabad;
  if (tag.includes("Mumbai")) return resourceStyles.tagMumbai;
  if (tag.includes("Chennai")) return resourceStyles.tagChennai;
  if (tag.includes("Pune")) return resourceStyles.tagPune;
  if (tag.includes("Website")) return resourceStyles.tagWebsite;
  if (tag.includes("Newsletter")) return resourceStyles.tagNewsletter;
  if (tag.includes("Membership")) return resourceStyles.tagMembership;
  return resourceStyles.tagLightning;
}

function getTagIcon(tag: string): string {
  if (tag.includes("Technology") || tag.includes("IT Services")) return "💻";
  if (tag.includes("Banking") || tag.includes("BFSI")) return "🏦";
  if (tag.includes("Engineering")) return "⚙️";
  if (tag.includes("Bengaluru") || tag.includes("Hyderabad") || tag.includes("Mumbai") || tag.includes("Chennai") || tag.includes("Pune")) return "📍";
  return "⚡";
}

// Helper function for category icons
function getCategoryIcon(categoryName: string): { icon: React.ReactNode; className: string } | null {
  // switch (categoryName) {
  //   case "All GCCs":
  //     return { icon: "📊", className: resourceStyles.iconAllGccs };
  //   case "By Industry":
  //     return { icon: "🏢", className: resourceStyles.iconIndustry };
  //   case "By Function":
  //     return { icon: "⚙️", className: resourceStyles.iconFunction };
  //   case "City":
  //     return { icon: "🌆", className: resourceStyles.iconCity };
  //   case "AI Readiness":
  //     return { icon: "🤖", className: resourceStyles.iconAiReadiness };
  //   case "Size/Scale":
  //     return { icon: "📏", className: resourceStyles.iconSize };
  //   case "Culture":
  //     return { icon: "🎯", className: resourceStyles.iconCulture };
  //   default:
  //     return { icon: "📋", className: resourceStyles.iconAllGccs };
  // }

  switch (categoryName) {
    
  case "All GCCs":
    return {
      icon: (
        <img
          src="https://static.thenounproject.com/png/8049610-200.png"
          alt="All GCCs"
          width={16}
          height={16}
        />
      ),
      className: resourceStyles.iconAllGccs,
    };

  case "By Industry":
    return {
      icon: (
        <img
          src="https://static.thenounproject.com/png/8052829-200.png"
          alt="Industry"
          width={16}
          height={16}
        />
      ),
      className: resourceStyles.iconIndustry,
    };

  case "By Function":
    return {
      icon: "⚙️",
      className: resourceStyles.iconFunction,
    };

  case "City":
    return {
      icon: (
        <img
          src="https://static.thenounproject.com/png/198234-200.png"
          alt="City"
          width={16}
          height={16}
        />
      ),
      className: resourceStyles.iconCity,
    };

  case "AI Readiness":
    return {
      icon: (
        <img
          src="https://static.thenounproject.com/png/7939559-200.png"
          alt="AI Readiness"
          width={16}
          height={16}
        />
      ),
      className: resourceStyles.iconAiReadiness,
    };

  case "Size/Scale":
    return {
      icon: (
        <img
          src="https://static.thenounproject.com/png/349559-200.png"
          alt="Size/Scale"
          width={16}
          height={16}
        />
      ),
      className: resourceStyles.iconSize,
    };

  case "Culture":
    return {
      icon: (
        <img
          src="https://static.thenounproject.com/png/7962919-200.png"
          alt="Culture"
          width={16}
          height={16}
        />
      ),
      className: resourceStyles.iconCulture,
    };

  default:
    return null;
}

}

function ResourceSection() {
  const [selected, setSelected] = useState("All GCCs");
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [selectedSub, setSelectedSub] = useState<string | null>(null);

  const filteredGCCs =
    selected === "All GCCs"
      ? gccs
      : selectedSub
      ? gccs.filter((g) => g.category === selected && g.tags?.includes(selectedSub))
      : gccs.filter((g) => g.category === selected);

  return (
    <div className={resourceStyles.sectionWrapper}>
      <aside className={resourceStyles.sidebar}>

      

        {categories.map((cat) => (
          <div key={cat.name} id={cat.id}>
            <button
              className={`${resourceStyles.sidebarButton} ${
                selected === cat.name ? resourceStyles.active : ""
              }`}
              onClick={() => {
                setSelected(cat.name);
                setSelectedSub(null);
                // Only open dropdown if not "All GCCs"
                if (cat.name !== "All GCCs") {
                  setOpenDropdown(openDropdown === cat.name ? null : cat.name);
                }
              }}
            >
              <div className={`${resourceStyles.categoryIcon} ${getCategoryIcon(cat.name)?.className ?? ""}`}>
                {getCategoryIcon(cat.name)?.icon}
              </div>
              {cat.name}
              {/* Show count for all categories */}
              <span className={resourceStyles.sidebarCount}>
                {cat.name === "All GCCs" ? gccs.length : cat.count}
              </span>
            </button>
            {/* Dropdown only for categories except All GCCs */}
            {cat.name !== "All GCCs" && openDropdown === cat.name && (
              <div className={resourceStyles.subDropdown}>
                {cat.sub && cat.sub.length > 0 ? (
                  cat.sub.map((sub) => (
                    <div
                      key={sub}
                      className={`${resourceStyles.subDropdownItem} ${
                        selectedSub === sub ? resourceStyles.active : ""
                      }`}
                      onClick={() => {
                        setSelected(cat.name);
                        setSelectedSub(sub);
                      }}
                    >
                      {sub}
                    </div>
                  ))
                ) : (
                  <div
                    className={resourceStyles.subDropdownItem}
                    style={{ color: "#888", fontStyle: "italic" }}
                  >
                    No subcategories
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </aside>

      <div className={resourceStyles.contentArea}>
        <div className={resourceStyles.grid}>
        {filteredGCCs.map((gcc) => {
          // If gcc has a slug, wrap in Link, otherwise return as is
          return gcc.slug ? (
            <Link
              key={gcc.title}
              href={`/company/${gcc.slug}`}
              className={resourceStyles.cardWrapper}
              onClick={() => console.log('Clicking on:', gcc.title, 'Slug:', gcc.slug)}
            >
              <div className={resourceStyles.card}>
                <div className={resourceStyles.cardHeader}>
                  <div className={resourceStyles.logoBox}>{gcc.icon}</div>
                  <span className={resourceStyles.cardLink}>↗</span>
                </div>
                <h2 className={resourceStyles.cardTitle}>{gcc.title}</h2>
                <p className={resourceStyles.cardDesc}>{gcc.desc}</p>
                <div className={resourceStyles.tags}>
                  {gcc.tags?.map((tag, index) => (
                    <span
                      key={index}
                      className={`${resourceStyles.tag} ${getTagClass(tag)}`}
                    >
                      {getTagIcon(tag)} {tag}
                    </span>
                  ))}
                </div>
              </div>
            </Link>
          ) : (
            <div key={gcc.title}>
              <div className={resourceStyles.card}>
                <div className={resourceStyles.cardHeader}>
                  <div className={resourceStyles.logoBox}>{gcc.icon}</div>
                  <span className={resourceStyles.cardLink} title="Open">↗</span>
                </div>
                <h2 className={resourceStyles.cardTitle}>{gcc.title}</h2>
                <p className={resourceStyles.cardDesc}>{gcc.desc}</p>
                <div className={resourceStyles.tags}>
                  {gcc.tags?.map((tag, index) => (
                    <span
                      key={index}
                      className={`${resourceStyles.tag} ${getTagClass(tag)}`}
                    >
                      {getTagIcon(tag)} {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
        </div>
      </div>
    </div>
  );
}

function BlogSection() {
  const blogPosts = [
    {
      id: 1,
      category: "Automation",
      title: "The best no-code automation tools to grow your startup",
      description: "No-code automation is gaining momentum, as businesses realize how easy it can be. You can use no-code automation tools to quickly develop scripts and other applications.",
      author: "Andy",
      date: "Nov 23, 2022",
      readTime: "5 min read",
      image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop&crop=center",
      gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    },
    {
      id: 2,
      category: "Low-code",
      title: "No code vs low code key differences for developers",
      description: "With the no-code movement taking shape fast, it's time to refocus. The future will be less about helping people build stuff and more about helping people build demand for stuff.",
      author: "Andy",
      date: "Nov 22, 2022",
      readTime: "5 min read",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center",
      gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
    },
    {
      id: 3,
      category: "Tech",
      title: "Guide to building your own no-code tech stack",
      description: "Once you have a goal in mind, the idea is to \"stack\" technology tools that will work toward that goal. There are essentially limitless ways to do this, but keeping it simple is the best.",
      author: "Erin Hannon",
      date: "Nov 21, 2022",
      readTime: "5 min read",
      image: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400&h=300&fit=crop&crop=center",
      gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
    }
  ];

  return (
    <section style={{
      background: "rgba(255, 255, 255, 1)",
      padding: "5rem 2rem",
      color: "#000000ff"
    }}>
      <div style={{
        maxWidth: "1250px",
        
        margin: "0 auto"
      }}>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: "3rem"
        }}>
          <h2 style={{
            fontSize: "2.5rem",
            fontWeight: "700",
            color: "#080707ff",
            margin: 0
          }}>
            Blog:The StrateGCC Imperative 
          </h2>
          <a href="#" style={{
            color: "#1de9b6",
            textDecoration: "none",
            fontSize: "1.1rem",
            fontWeight: "500",
            display: "flex",
            alignItems: "center",
            gap: "0.5rem"
          }}>
            View All →
          </a>
        </div>

        <div style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
          gap: "2rem"
        }}>
          {blogPosts.map((post) => (
            <article key={post.id} style={{
              background: "#1a1b1e",
              borderRadius: "16px",
              overflow: "hidden",
              border: "1px solid #2a2d31",
              transition: "all 0.3s ease",
              cursor: "pointer"
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = "translateY(-4px)";
              e.currentTarget.style.boxShadow = "0 12px 40px rgba(0, 0, 0, 0.3)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = "translateY(0)";
              e.currentTarget.style.boxShadow = "none";
            }}>
              <div style={{
                height: "200px",
                background: post.gradient,
                position: "relative",
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
              }}>
                <div style={{
                  width: "80px",
                  height: "80px",
                  background: "rgba(255, 255, 255, 0.2)",
                  borderRadius: "12px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  backdropFilter: "blur(10px)"
                }}>
                  <span style={{
                    fontSize: "2rem",
                    color: "#fff"
                  }}>
                    {post.category === "Automation" ? "🔧" :
                     post.category === "Low-code" ? "⚡" : "🚀"}
                  </span>
                </div>
                <span style={{
                  position: "absolute",
                  top: "1rem",
                  left: "1rem",
                  background: "rgba(0, 0, 0, 0.3)",
                  color: "#fff",
                  padding: "0.5rem 1rem",
                  borderRadius: "20px",
                  fontSize: "0.85rem",
                  fontWeight: "500",
                  backdropFilter: "blur(10px)"
                }}>
                  {post.category}
                </span>
              </div>

              <div style={{
                padding: "2rem"
              }}>
                <h3 style={{
                  fontSize: "1.5rem",
                  fontWeight: "600",
                  color: "#fff",
                  marginBottom: "1rem",
                  lineHeight: "1.4"
                }}>
                  {post.title}
                </h3>

                <p style={{
                  color: "#94a3b8",
                  lineHeight: "1.6",
                  marginBottom: "2rem",
                  fontSize: "1rem"
                }}>
                  {post.description}
                </p>

                <div style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "1rem"
                }}>
                  <div style={{
                    width: "40px",
                    height: "40px",
                    borderRadius: "50%",
                    background: "linear-gradient(135deg, #1de9b6, #00bfa5)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "#000",
                    fontWeight: "600",
                    fontSize: "1.1rem"
                  }}>
                    {post.author.charAt(0)}
                  </div>
                  <div>
                    <div style={{
                      color: "#fff",
                      fontWeight: "500",
                      fontSize: "0.95rem"
                    }}>
                      {post.author}
                    </div>
                    <div style={{
                      color: "#64748b",
                      fontSize: "0.85rem"
                    }}>
                      {post.date} • {post.readTime}
                    </div>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
}

function Footer() {
  return (
    <footer style={{
      background: "#1a1b1e",
      padding: "4rem 2rem 2rem",
      color: "#fff",
      borderTop: "1px solid #2a2d31"
    }}>
      <div style={{
        maxWidth: "1200px",
        margin: "0 auto",
        display: "grid",
        gridTemplateColumns: "2fr 1fr 1fr 1fr",
        gap: "3rem"
      }}>
        {/* Brand Section */}
        <div>
          <div style={{
            color: "#1de9b6",
            fontSize: "1.5rem",
            fontWeight: "bold",
            border: "2px solid #1de9b6",
            padding: "0.5rem 1rem",
            display: "inline-block",
            marginBottom: "1rem",
            borderRadius: "8px",
            background: "rgba(29, 233, 182, 0.1)"
          }}>
            Second HQ
          </div>
          <p style={{
            color: "#888",
            marginBottom: "2rem",
            lineHeight: "1.6"
          }}>
            Global Capability Centers directory for India
          </p>
          <div style={{ display: "flex", gap: "0.5rem" }}>
            <input
              type="email"
              placeholder="Your email address"
              style={{
                padding: "0.75rem",
                border: "none",
                borderRadius: "4px",
                background: "#3a3d41",
                color: "#fff",
                flex: 1,
                outline: "none"
              }}
            />
            <button style={{
              background: "linear-gradient(135deg, #1de9b6, #00bfa5)",
              color: "#000",
              border: "none",
              borderRadius: "8px",
              padding: "0.75rem 1.5rem",
              fontWeight: "600",
              cursor: "pointer",
              boxShadow: "0 4px 12px rgba(29, 233, 182, 0.3)"
            }}>
              Subscribe
            </button>
          </div>
        </div>

        {/* Navigation Section */}
        <div>
          <h3 style={{
            color: "#fff",
            marginBottom: "1.5rem",
            fontSize: "1.1rem"
          }}>
            Navigation
          </h3>
          <ul style={{
            listStyle: "none",
            padding: 0,
            margin: 0
          }}>
            {[
              "Home", "Inside India's GCCs", "By Industry", "By Function", "City",
              "AI Readiness", "Size/Scale", "Culture", "About", "Contact", "Submit GCC"
            ].map((item) => (
              <li key={item} style={{ marginBottom: "0.75rem" }}>
                <a href="#" style={{
                  color: "#888",
                  textDecoration: "none",
                  transition: "color 0.2s"
                }}>
                  {item}
                </a>
              </li>
            ))}
          </ul>
        </div>

        {/* Top Industries Section */}
        <div>
          <h3 style={{
            color: "#fff",
            marginBottom: "1.5rem",
            fontSize: "1.1rem"
          }}>
            Top Industries
          </h3>
          <ul style={{
            listStyle: "none",
            padding: 0,
            margin: 0
          }}>
            {[
              "Banking & Financial Services", "Technology & IT Services", "Healthcare", "Fintech", "Insurance",
              "Retail & CPG", "Manufacturing", "Energy", "Entertainment",
              "Logistics & Supply Chain", "EdTech"
            ].map((item) => (
              <li key={item} style={{ marginBottom: "0.75rem" }}>
                <a href="#" style={{
                  color: "#888",
                  textDecoration: "none",
                  transition: "color 0.2s"
                }}>
                  {item}
                </a>
              </li>
            ))}
          </ul>
        </div>

        {/* Top Cities Section */}
        <div>
          <h3 style={{
            color: "#fff",
            marginBottom: "1.5rem",
            fontSize: "1.1rem"
          }}>
            Top Cities
          </h3>
          <ul style={{
            listStyle: "none",
            padding: 0,
            margin: 0
          }}>
            {[
              { name: "Bengaluru", icon: "🌆" },
              { name: "Hyderabad", icon: "🌆" },
              { name: "Pune", icon: "🌆" },
              { name: "Chennai", icon: "�" },
              { name: "Gurugram", icon: "🌆" },
              { name: "Noida", icon: "🌆" },
              { name: "Mumbai", icon: "🌆" },
              { name: "Ahmedabad", icon: "🌆" },
              { name: "Coimbatore", icon: "🌆" },
              { name: "Kochi", icon: "�" }
            ].map((item) => (
              <li key={item.name} style={{ marginBottom: "0.75rem" }}>
                <a href="#" style={{
                  color: "#888",
                  textDecoration: "none",
                  display: "flex",
                  alignItems: "center",
                  gap: "0.5rem"
                }}>
                  <span>{item.icon}</span>
                  {item.name}
                </a>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </footer>
  );
}

function NewsletterSection() {
  return (
    <section style={{
       zIndex: 1,
      position: "relative",
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
      padding: "6rem 2rem",
      overflow: "0.8  "
    }}>
      {/* 3D Geometric Shapes Background */}
      <div style={{
         position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 0, // Add this
  pointerEvents: "none" 
      }}>
        {/* Large cube - top right */}
        <div style={{
          position: "absolute",
          top: "15%",
          right: "8%",
          width: "140px",
          height: "140px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))",
          transform: "rotate(45deg) perspective(800px) rotateX(30deg) rotateY(30deg)",
          borderRadius: "10px",
          border: "1px solid rgba(255,255,255,0.1)"
        }} />

        {/* Medium cube - bottom left */}
        <div style={{
          position: "absolute",
          bottom: "20%",
          left: "10%",
          width: "100px",
          height: "100px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))",
          transform: "rotate(-25deg) perspective(600px) rotateX(-20deg) rotateY(40deg)",
          borderRadius: "8px",
          border: "1px solid rgba(255,255,255,0.08)"
        }} />

        {/* Small cubes scattered */}
        <div style={{
          position: "absolute",
          top: "25%",
          left: "15%",
          width: "60px",
          height: "60px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))",
          transform: "rotate(60deg) perspective(500px) rotateX(50deg) rotateY(-25deg)",
          borderRadius: "6px",
          border: "1px solid rgba(255,255,255,0.06)"
        }} />

        <div style={{
          position: "absolute",
          top: "10%",
          left: "75%",
          width: "80px",
          height: "80px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.07), rgba(255,255,255,0.02))",
          transform: "rotate(-40deg) perspective(600px) rotateX(-35deg) rotateY(55deg)",
          borderRadius: "7px",
          border: "1px solid rgba(255,255,255,0.07)"
        }} />

        <div style={{
          position: "absolute",
          bottom: "15%",
          right: "20%",
          width: "90px",
          height: "90px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.05), rgba(255,255,255,0.01))",
          transform: "rotate(20deg) perspective(700px) rotateX(25deg) rotateY(-45deg)",
          borderRadius: "8px",
          border: "1px solid rgba(255,255,255,0.05)"
        }} />

        {/* Additional small cubes for more depth */}
        <div style={{
          position: "absolute",
          top: "60%",
          left: "5%",
          width: "50px",
          height: "50px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.04), rgba(255,255,255,0.01))",
          transform: "rotate(75deg) perspective(400px) rotateX(60deg) rotateY(-15deg)",
          borderRadius: "5px",
          border: "1px solid rgba(255,255,255,0.04)"
        }} />

        <div style={{
          position: "absolute",
          top: "40%",
          right: "5%",
          width: "70px",
          height: "70px",
          background: "linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))",
          transform: "rotate(-60deg) perspective(500px) rotateX(-40deg) rotateY(30deg)",
          borderRadius: "6px",
          border: "1px solid rgba(255,255,255,0.06)"
        }} />
      </div>

      {/* Content */}
      <div style={{
        position: "relative",
        zIndex: 10,
        maxWidth: "600px",
        margin: "0 auto",
        textAlign: "center"
      }}>
        <h2 style={{
          color: "#ffffff",
          fontWeight: 700,
          fontSize: "2.5rem",
          marginBottom: "1rem",
          lineHeight: "1.2",
          textShadow: "0 2px 4px rgba(0,0,0,0.1)"
        }}>
         Your Monthly Lens into India’s GCCs
        </h2>

        <p style={{
          color: "rgba(255,255,255,0.9)",
          fontSize: "1.1rem",
          marginBottom: "2.5rem",
          lineHeight: "1.6"
        }}>
         Each month, get a curated digest of the most important conversations, commentary, and catalog of India’s GCCs (Global Capability Centers).
        </p>

        {/* Email Subscription Form */}
        <div style={{
          display: "flex",
          justifyContent: "center",
          gap: "0",
          maxWidth: "450px",
          margin: "0 auto"
        }}>
          <input
            type="email"
            placeholder="Your email address"
            style={{
              padding: "1rem 1.5rem",
              fontSize: "1rem",
              borderRadius: "8px 0 0 8px",
              outline: "none",
              flex: 1,
              background: "rgba(0,0,0,0.4)",
              color: "#fff",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(255,255,255,0.2)",
              minHeight: "50px"
            }}
          />
          <button style={{
            background: "#1de9b6",
            color: "#000",
            border: "none",
            borderRadius: "0 8px 8px 0",
            padding: "1rem 2rem",
            fontWeight: 600,
            fontSize: "1rem",
            cursor: "pointer",
            transition: "all 0.3s ease",
            textTransform: "uppercase",
            letterSpacing: "0.5px",
            minHeight: "50px"
          }}>
            Subscribe
          </button>
        </div>
      </div>
    </section>
  );
}

export default function Home() {
  return (
    <div
      style={{
        minHeight: "100vh",
        width: "100%",
        background: "#ffffff",
        overflowX: "hidden",
      }}
    >
      <div id="home" className={styles.heroSection}>
        <TopNavbar />
        <div style={{ padding: "3rem 0", textAlign: "center", margin: "10rem 0 0 0" }}>
          <h1 style={{ color: "#f8fafc", fontWeight: 700, fontSize: "37.44px" }}>
            Every <span style={{ color: "#1de9b6" }}>Global Capability Center (GCC)</span> in India.<br></br> Documented & Decoded.
          </h1>
         
          <p style={{ color: "#fff", fontFamily: "sans-serif", fontSize: "19px", margin: "1rem 0 12rem 0" }}>
            Explore the work, talent, AI-readiness, culture, and employer brand salience of every global enterprise&apos;s (un)official <br></br>second HQ: their India Global Capability Center.
          </p>
        </div>
        <svg
          width="100%"
          height="120"
          viewBox="0 0 1440 120"
          style={{
            display: "block",
            position: "absolute",
            bottom: 0,
            left: 0,
            zIndex: 2,
          }}
          preserveAspectRatio="none"
        >
          {/* <path
            fill="#ffffff"
            d="
              M0,0
              Q720,160 1440,0
              L1440,120
              L0,120
              Z
            "
          
            /> */
          
            <path fill="#ffffff"
              d="
    M0,0
    C480,40 960,40 1440,0
    L1440,120
    L0,120
    Z
  "
/>

          
          }
        </svg>
      </div>

      {/* About GCC Section */}
      <section style={{
        padding: "2rem 2rem",
        backgroundColor: "#ffffff",
        textAlign: "left",
        maxWidth: "1200px",
        margin: "0 auto"
      }}>
        <h3 style={{
          fontSize: "2.5rem",
          fontWeight: "700",
          color: "#1f2937",
          marginBottom: "1.5rem",
          fontFamily: "system-ui, -apple-system, sans-serif"
        }}>
          Documented & Decoded.
        </h3>
        <p style={{
          fontSize: "1.2rem",
          lineHeight: "1.8",
          color: "#4b5563",
          maxWidth: "900px",
          margin: "0",
          fontFamily: "system-ui, -apple-system, sans-serif"
        }}>
          Global Capability Centers (or GCCs) in India aren&apos;t back offices anymore. They&apos;re product hubs, AI epicentres, innovation studios, ownership centers, engineering powerhouses, R&D engines, analytics arenas, and so much more!
          <br/><br/>
          They&apos;re fast becoming the de facto Second HQ for some of the largest enterprises in the world.
          <br/><br/>
          This is an attempt to discover and decode these GCCs. What&apos;s the work? Why does it matter? How will it shape the future?
        </p>
      </section>

      <ResourceSection />
      <BlogSection />
      <NewsletterSection />
      <Footer />
    </div>
  );
}