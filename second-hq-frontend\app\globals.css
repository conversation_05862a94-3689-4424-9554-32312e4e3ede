@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

:root {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  /* --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono); */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Prevent horizontal scroll on all elements */
* {
  max-width: 100%;
}

/* Ensure containers don't overflow */
div, section, nav, header, main, footer {
  max-width: 100%;
  overflow-x: hidden;
}

/* Fix for input elements that might cause overflow */
input, button {
  max-width: 100%;
}

/* Email input styling */
.email-input::placeholder {
  color: #999;
  opacity: 1;
}

.email-input:focus {
  border-color: #1de9b6;
  box-shadow: 0 0 0 2px rgba(29, 233, 182, 0.2);
}

/* Container utility class */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
}

/* Mobile responsive fixes */
@media (max-width: 768px) {
  body {
    overflow-x: hidden;
  }

  .container {
    padding: 0 16px;
  }

  /* Ensure all flex containers are responsive */
  div[style*="display: flex"] {
    flex-wrap: wrap;
  }

  /* Make sure form elements are responsive */
  input[type="email"] {
    min-width: 0;
    flex: 1;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }

  /* Stack form elements on very small screens */
  div[style*="display: flex"] {
    flex-direction: column;
    gap: 10px;
  }

  /* Make email input and button full width on mobile */
  .email-input {
    border-radius: 10px !important;
  }

  /* Adjust button for mobile */
  button[style*="SUBSCRIBE"] {
    border-radius: 10px !important;
    padding: 0 20px !important;
    font-size: 0.85rem !important;
  }
}
/* globals.css */

:root {
  --font-heading: var(--font-dm-sans);
  --font-body: var(--font-manrope);
}

body {
  font-family: var(--font-body), sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading), sans-serif;
}

