{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/Documents/GitHub/second-hq/second-hq-frontend/app/TopNavbar.module.css"], "sourcesContent": [".navbar {\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 100;\r\n  background-color: #ffffff;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 2rem;\r\n  height: 72px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.logo {\r\n  font-weight: 700;\r\n  font-size: 1.5rem;\r\n  color: #16d1aa;\r\n  margin-right: 0;\r\n  border: none;\r\n  padding: 0;\r\n  background: transparent;\r\n  letter-spacing: 0;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.logo:hover {\r\n  background: transparent;\r\n  transform: none;\r\n  opacity: 0.8;\r\n}\r\n\r\n.links {\r\n  display: flex;\r\n  gap: 2rem;\r\n  align-items: center;\r\n}\r\n\r\n.navLink {\r\n  color: #374151;\r\n  text-decoration: none;\r\n  padding: 0.5rem 0;\r\n  border-radius: 0;\r\n  font-weight: 500;\r\n  font-size: 0.95rem;\r\n  transition: color 0.2s ease;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.navLink:hover,\r\n.navLink:focus {\r\n  background: transparent;\r\n  color: #16d1aa;\r\n  transform: none;\r\n}\r\n\r\n.active {\r\n  background: transparent;\r\n  color: #16d1aa;\r\n  font-weight: 600;\r\n}\r\n\r\n.subscribeBtn {\r\n  background: #16d1aa;\r\n  color: #ffffff;\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 0.75rem 1.5rem;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  margin-left: 0;\r\n  cursor: pointer;\r\n  transition: background 0.2s ease;\r\n  box-shadow: none;\r\n}\r\n\r\n.subscribeBtn:hover {\r\n  background: #14b896;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.heroSection {\r\n  position: relative;\r\n  background: #ffffff;\r\n  z-index: 1;\r\n  padding: 4rem 2rem 6rem;\r\n  text-align: center;\r\n  overflow: hidden;\r\n}\r\n\r\n/* Add responsive navbar */\r\n@media (max-width: 768px) {\r\n  .navbar {\r\n    padding: 0 1rem;\r\n    height: 60px;\r\n  }\r\n\r\n  .logo {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .links {\r\n    gap: 1rem;\r\n  }\r\n\r\n  .navLink {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .subscribeBtn {\r\n    padding: 0.5rem 1rem;\r\n    font-size: 0.8rem;\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;AAcA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;;;;AAUA;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}