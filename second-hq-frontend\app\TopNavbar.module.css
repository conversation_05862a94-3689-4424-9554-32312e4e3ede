.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: transparent;
  /* backdrop-filter: blur(20px); */
  /* border-bottom: 1px solid #2a2d31; */
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  padding: 0 20px;
  margin-top: 0 0 16px;
  height: 72px;
  /* box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1); */
}

.navContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  height: 100%;
}

.logo {
  font-weight: 500;
  font-size: 18px;
  color: #1de9b6;
  border: 3px solid #1de9b6;
  padding: 2px 8px;
  /* border-radius: 16px; */
  background: rgba(29, 233, 182, 0.1);
  letter-spacing: 1px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

/* .logo:hover {
  background: rgba(29, 233, 182, 0.2);
  transform: scale(0.05);
} */

.links {
  display: flex;
  gap: 2px;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.navLink {
  color: #feffff;
  text-decoration: none;
  padding: 4px 12px;
  border-radius: 16px;
  font-weight: 500;
  font-size: 16px;
 font-family: 'figtree', sans-serif;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
}

.navLink:hover,
.navLink:focus {
  background: rgba(29, 233, 182, 0.1);
  color: #1de9b6;
  transform: translateY(-1px);
}

.active {
  background: rgba(29, 233, 182, 0.15);
  color: #1de9b6;
}

.subscribeBtn {
  background: linear-gradient(135deg, #1de9b6, #00bfa5);
  color: #000;
  border: none;
  border-radius: 16px;
  padding: 5px 32px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(29, 233, 182, 0.3);
  flex-shrink: 0;
}

.subscribeBtn:hover {
  background: linear-gradient(135deg, #00bfa5, #009688);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(29, 233, 182, 0.4);
}

.heroSection {
  position: relative;
  background: linear-gradient(180deg, rgba(35,20,107,0.75) 0%, rgba(35,20,107,0.75) 100%),
    url('/images/hqbackground.png') center/cover no-repeat;
  z-index: 1;
  padding-bottom: 0;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .navContainer {
    max-width: 100%;
    padding: 0 20px;
  }

  .logo {
    font-size: 16px;
  }

  .navLink {
    padding: 6px 10px;
    font-size: 14px;
  }

  .subscribeBtn {
    padding: 12px 24px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 16px;
    height: 70px;
  }

  .navContainer {
    justify-content: space-between;
  }

  .logo {
    font-size: 18px;
    padding: 6px 12px;
  }

  .links {
    display: none;
  }

  .subscribeBtn {
    display: none;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 0 12px;
    height: 65px;
  }

  .navContainer {
    padding: 0;
  }

  .logo {
    font-size: 16px;
    padding: 4px 8px;
  }
}

@media (max-width: 360px) {
  .navbar {
    padding: 0 8px;
  }

  .logo {
    font-size: 14px;
    padding: 4px 6px;
  }
}