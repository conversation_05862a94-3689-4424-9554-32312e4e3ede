.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  height: 72px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.logo {
  font-weight: 700;
  font-size: 1.5rem;
  color: #16d1aa;
  margin-right: 0;
  border: none;
  padding: 0;
  background: transparent;
  letter-spacing: 0;
  transition: all 0.3s ease;
}

.logo:hover {
  background: transparent;
  transform: none;
  opacity: 0.8;
}

.links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.navLink {
  color: #374151;
  text-decoration: none;
  padding: 0.5rem 0;
  border-radius: 0;
  font-weight: 500;
  font-size: 0.95rem;
  transition: color 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
}

.navLink:hover,
.navLink:focus {
  background: transparent;
  color: #16d1aa;
  transform: none;
}

.active {
  background: transparent;
  color: #16d1aa;
  font-weight: 600;
}

.subscribeBtn {
  background: #16d1aa;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.9rem;
  margin-left: 0;
  cursor: pointer;
  transition: background 0.2s ease;
  box-shadow: none;
}

.subscribeBtn:hover {
  background: #14b896;
  transform: none;
  box-shadow: none;
}

.heroSection {
  position: relative;
  background: #ffffff;
  z-index: 1;
  padding: 4rem 2rem 6rem;
  text-align: center;
  overflow: hidden;
}

/* Add responsive navbar */
@media (max-width: 768px) {
  .navbar {
    padding: 0 1rem;
    height: 60px;
  }

  .logo {
    font-size: 1.25rem;
  }

  .links {
    gap: 1rem;
  }

  .navLink {
    font-size: 0.85rem;
  }

  .subscribeBtn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}