/* [project]/Documents/GitHub/second-hq/second-hq-frontend/app/TopNavbar.module.css [app-client] (css) */
.TopNavbar-module__RqcZ4q__navbar {
  z-index: 100;
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  height: 72px;
  padding: 0 2rem;
  display: flex;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
}

.TopNavbar-module__RqcZ4q__logo {
  color: #16d1aa;
  letter-spacing: 0;
  background: none;
  border: none;
  margin-right: 0;
  padding: 0;
  font-size: 1.5rem;
  font-weight: 700;
  transition: all .3s;
}

.TopNavbar-module__RqcZ4q__logo:hover {
  opacity: .8;
  background: none;
  transform: none;
}

.TopNavbar-module__RqcZ4q__links {
  align-items: center;
  gap: 2rem;
  display: flex;
}

.TopNavbar-module__RqcZ4q__navLink {
  color: #374151;
  border-radius: 0;
  align-items: center;
  padding: .5rem 0;
  font-size: .95rem;
  font-weight: 500;
  text-decoration: none;
  transition: color .2s;
  display: flex;
  position: relative;
}

.TopNavbar-module__RqcZ4q__navLink:hover, .TopNavbar-module__RqcZ4q__navLink:focus {
  color: #16d1aa;
  background: none;
  transform: none;
}

.TopNavbar-module__RqcZ4q__active {
  color: #16d1aa;
  background: none;
  font-weight: 600;
}

.TopNavbar-module__RqcZ4q__subscribeBtn {
  color: #fff;
  cursor: pointer;
  box-shadow: none;
  background: #16d1aa;
  border: none;
  border-radius: 6px;
  margin-left: 0;
  padding: .75rem 1.5rem;
  font-size: .9rem;
  font-weight: 600;
  transition: background .2s;
}

.TopNavbar-module__RqcZ4q__subscribeBtn:hover {
  box-shadow: none;
  background: #14b896;
  transform: none;
}

.TopNavbar-module__RqcZ4q__heroSection {
  z-index: 1;
  text-align: center;
  background: #fff;
  padding: 4rem 2rem 6rem;
  position: relative;
  overflow: hidden;
}

@media (max-width: 768px) {
  .TopNavbar-module__RqcZ4q__navbar {
    height: 60px;
    padding: 0 1rem;
  }

  .TopNavbar-module__RqcZ4q__logo {
    font-size: 1.25rem;
  }

  .TopNavbar-module__RqcZ4q__links {
    gap: 1rem;
  }

  .TopNavbar-module__RqcZ4q__navLink {
    font-size: .85rem;
  }

  .TopNavbar-module__RqcZ4q__subscribeBtn {
    padding: .5rem 1rem;
    font-size: .8rem;
  }
}

/*# sourceMappingURL=Documents_GitHub_second-hq_second-hq-frontend_app_TopNavbar_module_css_bad6b30c._.single.css.map*/