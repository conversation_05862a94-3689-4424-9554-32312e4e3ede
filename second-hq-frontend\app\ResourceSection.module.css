.sectionWrapper {
  display: flex;
  background: #f9fafb;
  padding: 3rem 2rem;
  min-height: 90vh;
  gap: 3rem;
  max-width: 1320px;
  margin: 0 auto;
  width: 100%;
}

.sidebar {
  min-width: 280px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: fit-content;
  position: sticky;
  top: 6rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebarButton {
  background: transparent;
  border: none;
  color: #6b7280;
  font-size: 0.9rem;
  border-radius: 6px;
  padding: 0.75rem 1rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
  font-weight: 500;
  position: relative;
}

.sidebarButton.active,
.sidebarButton:hover {
  background: #f3f4f6;
  color: #16d1aa;
  transform: none;
}

.sidebarCount {
  background: #16d1aa;
  color: #ffffff;
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  margin-left: auto;
  font-size: 0.75rem;
  font-weight: 600;
}

.sidebarCategory {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #6b7280;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.sidebarCategory:hover {
  background: #f3f4f6;
  color: #16d1aa;
}

.grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.card {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  color: #374151;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
  min-height: 200px;
}

.card:hover {
  border: 1px solid #16d1aa;
  box-shadow: 0 4px 12px rgba(22, 209, 170, 0.15);
  transform: translateY(-2px);
}

.cardIcon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  background: #f3f4f6;
  color: #374151;
}

.cardTitle {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #111827;
  line-height: 1.4;
}

.cardDesc {
  font-size: 0.95rem;
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.cardLink {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  color: #9ca3af;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.cardLink:hover {
  color: #16d1aa;
  transform: none;
}

/* Add category badge styling */
.categoryBadge {
  display: inline-block;
  background: #f3f4f6;
  color: #6b7280;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.5rem;
  border: 1px solid #e5e7eb;
}

/* Responsive design */
@media (max-width: 1024px) {
  .sectionWrapper {
    flex-direction: column;
    padding: 2rem 1.5rem;
    gap: 2rem;
    max-width: 100%;
  }

  .sidebar {
    min-width: unset;
    position: static;
    padding: 1.5rem;
  }

  .grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .sectionWrapper {
    padding: 1.5rem 1rem;
    gap: 1.5rem;
  }

  .sidebar {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 1.5rem;
    min-height: 180px;
  }
}

/* Add subtle animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.6s ease forwards;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }
.card:nth-child(5) { animation-delay: 0.5s; }
.card:nth-child(6) { animation-delay: 0.6s; }

/* Dropdown styles */
.subDropdown {
  margin-top: 0.5rem;
  margin-left: 1rem;
  border-left: 2px solid rgba(29, 233, 182, 0.2);
  padding-left: 1rem;
}

.subDropdownItem {
  padding: 0.75rem 1rem;
  color: #94a3b8;
  cursor: pointer;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  margin-bottom: 0.25rem;
}

.subDropdownItem:hover,
.subDropdownItem.active {
  background: rgba(29, 233, 182, 0.1);
  color: #1de9b6;
}