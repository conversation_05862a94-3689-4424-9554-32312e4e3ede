.sectionWrapper {
  display: flex;
  background: #ffffff;
  padding: 1rem 2rem;
  height: 90vh;
  gap: 0;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  align-items: flex-start;
  position: relative;
  overflow: hidden;
}

/* Hide all scrollbars globally within this section */
.sectionWrapper * {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.sectionWrapper *::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.sidebar {
  width: 300px;
  min-width: 300px;
  max-width: 300px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  height: calc(90vh - 2rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  position: sticky;
  top: 1rem;
  align-self: flex-start;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.sidebar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.sidebarButton {
  background: transparent;
  border: none;
  color: #374151;
  font-size: 1rem;
  border-radius: 8px;
  padding: 1rem 1.25rem;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
  font-weight: 500;
  position: relative;
  width: 100%;
  min-height: 52px;
  overflow: hidden;
}

/* Chevron at right */
.chevron {
  margin-left: auto;
  font-size: 28px;
  color: #9ca3af;
  transition: transform 0.2s ease, color 0.2s ease;
}
.chevronOpen {
  transform: rotate(90deg);
  color: #6b7280;
}

.sidebarButton.active {
  background: #f3f4f6;
  color: #111827;
  border: 1px solid #d1d5db;
  font-weight: 600;
}

.sidebarButton:hover {
  background: #f9fafb;
  color: #111827;
}

.sidebarCount {
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  padding: 0.25rem 0.5rem;
  margin-left: auto;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.sidebarCategory {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #9ca3af;
  font-size: 1rem;
  cursor: pointer;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.sidebarCategory:hover {
  background: #f9fafb;
  color: #111827;
}

/* Category icon styles */
.categoryIcon {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.iconAllGccs {
  background: #f3f4f6;
  color: #6b7280;
}

.iconIndustry {
  background: #fef3c7;
  color: #d97706;
}

.iconFunction {
  background: #dbeafe;
  color: #2563eb;
}

.iconCity {
  background: #dcfce7;
  color: #16a34a;
}

.iconAiReadiness {
  background: #f3e8ff;
  color: #9333ea;
}

.iconSize {
  background: #fce7f3;
  color: #ec4899;
}

.iconCulture {
  background: #ecfdf5;
  color: #10b981;
}

.contentArea {
  flex: 1;
  margin-left: 2rem;
  padding: 0;
  background: #ffffff;
  height: calc(90vh - 2rem);
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.contentArea::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  align-items: stretch;
  max-width: 100%;
  margin: 0;
}

.card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.08);
  padding: 16px;
  border: 1px solid #eee;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  transform: translateY(-2px);
}

.cardHeader {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
}

.logoBox {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #ff00cc, #3333ff);
  color: white;
  font-size: 24px;
}

.cardTitle {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #1f2937;
  line-height: 1.3;
}

.cardDesc {
  font-size: 14px;
  line-height: 1.6;
  color: #6b7280;
  margin-bottom: 16px;
  margin-top: 0;
  flex-grow: 1;
}

.cardLink {
  font-size: 14px;
  color: #888;
  text-decoration: none;
  transition: all 0.2s ease;
}

.cardLink:hover {
  color: #222;
}

.tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin: 0;
  margin-top: auto;
}

.tag {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 8px;
  font-weight: 500;
}

.tagLightning {
  background: #e6f9f7;
  color: #00c2a8;
}

.tagWebsite {
  background: #e6ffe6;
  color: #28a745;
}

.tagNewsletter {
  background: #fff0e6;
  color: #ff6600;
}

.tagMembership {
  background: #ffe6ff;
  color: #d633d6;
}

.tagTechnology {
  background: #e6f3ff;
  color: #0066cc;
}

.tagBfsi {
  background: #fff0e6;
  color: #ff8800;
}

.tagEngineering {
  background: #f0e6ff;
  color: #8800cc;
}

.tagBengaluru {
  background: #e6ffe6;
  color: #00aa00;
}

.tagHyderabad {
  background: #ffe6e6;
  color: #cc0000;
}

.tagMumbai {
  background: #f0f0ff;
  color: #4444cc;
}

.tagChennai {
  background: #fff5e6;
  color: #cc6600;
}

.tagPune {
  background: #f5e6ff;
  color: #9933cc;
}

/* Remove old cardIcon styles - no longer needed */

/* Responsive design */
@media (max-width: 1024px) {
  .sectionWrapper {
    flex-direction: column;
    padding: 2rem 1.5rem;
    gap: 2rem;
    max-width: 100%;
  }

  .sidebar {
    position: static;
    width: 100%;
    min-width: unset;
    max-width: unset;
    height: auto;
    padding: 1.5rem;
    max-height: none;
  }

  .contentArea {
    margin-left: 0;
  }

  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .sectionWrapper {
    padding: 2rem 1rem;
    gap: 2rem;
  }

  .sidebar {
    padding: 1rem;
  }

  .categoryIcon {
    width: 20px;
    height: 20px;
    font-size: 12px;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 16px;
    max-width: 100%;
    min-height: 260px;
  }

  .logoBox {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .cardTitle {
    font-size: 18px;
  }

  .cardDesc {
    font-size: 13px;
  }
}

/* Simplified animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.3s ease forwards;
  transition: all 0.2s ease;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cardWrapper {
  text-decoration: none;
  color: inherit;
  display: block;
  width: 100%;
  height: 100%;
}

.cardWrapper:hover {
  text-decoration: none;
  color: inherit;
}

.cardBadge {
  background: #16d1aa;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.cardWrapper:hover .cardBadge {
  background: #14b8a6;
  transform: translateX(2px);
}

/* Dropdown styles */
.subDropdown {
  margin-top: 0.5rem;
  margin-left: 1rem;
  border-left: 2px solid #16d1aa;
  padding-left: 1rem;
  width: calc(100% - 2rem);
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.subDropdown::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.subDropdownItem {
  padding: 0.5rem 0.75rem;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  font-size: 0.8rem;
  transition: all 0.2s ease;
  white-space: pre-line;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  flex-shrink: 0;
  text-align: center;
  line-height: 1.3;
}

.subDropdownItem:hover,
.subDropdownItem.active {
  background: #16d1aa;
  color: #ffffff;
  border-color: #16d1aa;
}