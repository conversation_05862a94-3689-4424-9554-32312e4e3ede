module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/Documents/GitHub/second-hq/second-hq-frontend/app/TopNavbar.module.css [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "active": "TopNavbar-module__RqcZ4q__active",
  "heroSection": "TopNavbar-module__RqcZ4q__heroSection",
  "links": "TopNavbar-module__RqcZ4q__links",
  "logo": "TopNavbar-module__RqcZ4q__logo",
  "navLink": "TopNavbar-module__RqcZ4q__navLink",
  "navbar": "TopNavbar-module__RqcZ4q__navbar",
  "subscribeBtn": "TopNavbar-module__RqcZ4q__subscribeBtn",
});
}),
"[project]/Documents/GitHub/second-hq/second-hq-frontend/app/ResourceSection.module.css [app-ssr] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "active": "ResourceSection-module__fanLqG__active",
  "card": "ResourceSection-module__fanLqG__card",
  "cardDesc": "ResourceSection-module__fanLqG__cardDesc",
  "cardIcon": "ResourceSection-module__fanLqG__cardIcon",
  "cardLink": "ResourceSection-module__fanLqG__cardLink",
  "cardTitle": "ResourceSection-module__fanLqG__cardTitle",
  "categoryBadge": "ResourceSection-module__fanLqG__categoryBadge",
  "fadeInUp": "ResourceSection-module__fanLqG__fadeInUp",
  "grid": "ResourceSection-module__fanLqG__grid",
  "sectionWrapper": "ResourceSection-module__fanLqG__sectionWrapper",
  "sidebar": "ResourceSection-module__fanLqG__sidebar",
  "sidebarButton": "ResourceSection-module__fanLqG__sidebarButton",
  "sidebarCategory": "ResourceSection-module__fanLqG__sidebarCategory",
  "sidebarCount": "ResourceSection-module__fanLqG__sidebarCount",
  "subDropdown": "ResourceSection-module__fanLqG__subDropdown",
  "subDropdownItem": "ResourceSection-module__fanLqG__subDropdownItem",
});
}),
"[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>Home
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/Documents/GitHub/second-hq/second-hq-frontend/app/TopNavbar.module.css [app-ssr] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/Documents/GitHub/second-hq/second-hq-frontend/app/ResourceSection.module.css [app-ssr] (css module)");
"use client";
;
;
;
;
const navLinks = [
    {
        name: "Home",
        href: "#"
    },
    {
        name: "Inside India's GCCs",
        href: "#"
    },
    {
        name: "By Industry",
        href: "#"
    },
    {
        name: "By Function",
        href: "#"
    },
    {
        name: "City",
        href: "#"
    },
    {
        name: "AI Readiness",
        href: "#"
    },
    {
        name: "Size/Scale",
        href: "#"
    },
    {
        name: "Culture",
        href: "#"
    }
];
function TopNavbar() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navbar,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].logo,
                children: "Second HQ"
            }, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 21,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].links,
                children: navLinks.map((link)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                        href: link.href,
                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].navLink} ${link.name === "Home" ? __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ""}`,
                        children: link.name
                    }, link.name, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 24,
                        columnNumber: 21
                    }, this))
            }, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 22,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subscribeBtn,
                children: "Subscribe"
            }, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 33,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
        lineNumber: 20,
        columnNumber: 9
    }, this);
}
const categories = [
    {
        name: "All GCCs"
    },
    {
        name: "By Industry",
        sub: [
            "Banking & Financial Services (BFSI)",
            "Fintech",
            "Insurance",
            "Healthcare",
            "Pharma & MedTech",
            "Retail & CPG",
            "Energy",
            "Manufacturing",
            "Entertainment",
            "Travel & Hospitality",
            "Logistics & Supply Chain",
            "Enterprise SaaS",
            "Cloud & Infra",
            "Cybersecurity",
            "Gaming",
            "EdTech",
            "Public Sector",
            "Technology & IT Services",
            "Professional & Business Services",
            "Automotive & Industrial"
        ]
    },
    {
        name: "By Function",
        sub: [
            "Engineering Hub",
            "Product & Design Studio",
            "Data/ML Center",
            "R&D Lab",
            "Risk & Compliance COE",
            "Finance Shared Services",
            "Customer Ops",
            "Supply Chain COE",
            "Security Ops Center",
            "Innovation Garage",
            "Platform/Infra Hub"
        ]
    },
    {
        name: "City",
        sub: [
            "Bengaluru",
            "Hyderabad",
            "Pune",
            "Chennai",
            "Gurugram",
            "Noida",
            "Mumbai",
            "Ahmedabad",
            "Coimbatore",
            "Kochi",
            "Jaipur",
            "Chandigarh"
        ]
    },
    {
        name: "AI Readiness",
        sub: [
            "AI-Native",
            "AI Platform/CoE",
            "Data-Driven",
            "AI-Adopting",
            "Legacy-Modernizing",
            "Regulated-AI",
            "Early Stage-AI"
        ]
    },
    {
        name: "Size/Scale",
        sub: [
            "0-200 Employees",
            "200-500 Employees",
            "500-1,500 Employees",
            "1,500-3,000 Employees",
            "3,000+ Employees"
        ]
    },
    {
        name: "Culture",
        sub: [
            "Builder Culture",
            "Global Ownership",
            "High Experimentation",
            "Strong Learning",
            "Hybrid-First",
            "Inclusion Leader",
            "Low-Hierarchy",
            "Process-Driven",
            "Customer-Obsessed"
        ]
    }
];
const gccs = [
    {
        title: "Microsoft India Development Center",
        desc: "One of Microsoft's largest R&D centers outside the US, focusing on cloud, AI, and enterprise solutions with over 8,000 employees.",
        icon: "�",
        category: "By Industry",
        link: "#",
        tags: [
            "Technology & IT Services",
            "Hyderabad"
        ]
    },
    {
        title: "Goldman Sachs Bengaluru",
        desc: "Major technology and operations hub for Goldman Sachs, handling global trading systems, risk management, and digital innovation.",
        icon: "🏦",
        category: "By Industry",
        link: "#",
        tags: [
            "Banking & Financial Services (BFSI)",
            "Bengaluru"
        ]
    },
    {
        title: "Google India",
        desc: "Google's largest engineering center outside the US, working on products like Google Pay, Search, and Android with AI-first approach.",
        icon: "🔍",
        category: "By Function",
        link: "#",
        tags: [
            "Engineering Hub",
            "Bengaluru"
        ]
    },
    {
        title: "Amazon Development Center",
        desc: "Amazon's largest software development center outside Seattle, focusing on AWS, Alexa, and e-commerce platforms.",
        icon: "�",
        category: "City",
        link: "#",
        tags: [
            "Hyderabad",
            "Bengaluru"
        ]
    },
    {
        title: "Walmart Global Tech",
        desc: "Walmart's technology hub driving digital transformation with focus on e-commerce, supply chain, and data analytics.",
        icon: "�",
        category: "AI Readiness",
        link: "#",
        tags: [
            "AI Platform/CoE",
            "Bengaluru"
        ]
    },
    {
        title: "JPMorgan Chase Technology Center",
        desc: "Global technology center focusing on digital banking, blockchain, and financial technology innovations.",
        icon: "�",
        category: "Size/Scale",
        link: "#",
        tags: [
            "Large (1001-5000)",
            "Mumbai"
        ]
    }
];
function ResourceSection() {
    const [selected, setSelected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("All GCCs");
    const [openDropdown, setOpenDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedSub, setSelectedSub] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const filteredGCCs = selected === "All GCCs" ? gccs : selectedSub ? gccs.filter((g)=>g.category === selected && g.tags?.includes(selectedSub)) : gccs.filter((g)=>g.category === selected);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sectionWrapper,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sidebar,
                children: categories.map((cat)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: `${__TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sidebarButton} ${selected === cat.name ? __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ""}`,
                                onClick: ()=>{
                                    setSelected(cat.name);
                                    setSelectedSub(null);
                                    // Only open dropdown if not "All GCCs"
                                    if (cat.name !== "All GCCs") {
                                        setOpenDropdown(openDropdown === cat.name ? null : cat.name);
                                    }
                                },
                                style: {
                                    color: selected === cat.name ? "#1de9b6" : undefined,
                                    fontWeight: selected === cat.name ? 600 : undefined,
                                    justifyContent: "flex-start",
                                    width: "100%"
                                },
                                children: [
                                    cat.name,
                                    cat.name === "All GCCs" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].sidebarCount,
                                        children: gccs.length
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 147,
                                        columnNumber: 17
                                    }, this),
                                    cat.name !== "All GCCs" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            marginLeft: "auto",
                                            fontSize: 18
                                        },
                                        children: openDropdown === cat.name ? "▲" : "▼"
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 151,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 125,
                                columnNumber: 13
                            }, this),
                            cat.name !== "All GCCs" && openDropdown === cat.name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdown,
                                children: cat.sub && cat.sub.length > 0 ? cat.sub.map((sub)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `${__TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownItem} ${selectedSub === sub ? __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].active : ""}`,
                                        onClick: ()=>{
                                            setSelected(cat.name);
                                            setSelectedSub(sub);
                                        },
                                        children: sub
                                    }, sub, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 161,
                                        columnNumber: 21
                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].subDropdownItem,
                                    style: {
                                        color: "#888",
                                        fontStyle: "italic"
                                    },
                                    children: "No subcategories"
                                }, void 0, false, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 175,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 158,
                                columnNumber: 15
                            }, this)
                        ]
                    }, cat.name, true, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 124,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 122,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].grid,
                children: filteredGCCs.map((gcc)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].card,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cardIcon,
                                children: gcc.icon
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 190,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cardTitle,
                                children: gcc.title
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 191,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cardDesc,
                                children: gcc.desc
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 192,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                href: gcc.link,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$ResourceSection$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].cardLink,
                                target: "_blank",
                                rel: "noopener noreferrer",
                                title: "Open",
                                children: "↗"
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, this)
                        ]
                    }, gcc.title, true, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 189,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 187,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
        lineNumber: 121,
        columnNumber: 5
    }, this);
}
function BlogSection() {
    const blogPosts = [
        {
            id: 1,
            category: "Automation",
            title: "The best no-code automation tools to grow your startup",
            description: "No-code automation is gaining momentum, as businesses realize how easy it can be. You can use no-code automation tools to quickly develop scripts and other applications.",
            author: "Andy",
            date: "Nov 23, 2022",
            readTime: "5 min read",
            image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop&crop=center",
            gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        },
        {
            id: 2,
            category: "Low-code",
            title: "No code vs low code key differences for developers",
            description: "With the no-code movement taking shape fast, it's time to refocus. The future will be less about helping people build stuff and more about helping people build demand for stuff.",
            author: "Andy",
            date: "Nov 22, 2022",
            readTime: "5 min read",
            image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center",
            gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
        },
        {
            id: 3,
            category: "Tech",
            title: "Guide to building your own no-code tech stack",
            description: "Once you have a goal in mind, the idea is to \"stack\" technology tools that will work toward that goal. There are essentially limitless ways to do this, but keeping it simple is the best.",
            author: "Erin Hannon",
            date: "Nov 21, 2022",
            readTime: "5 min read",
            image: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400&h=300&fit=crop&crop=center",
            gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        style: {
            background: "#0a0b0d",
            padding: "5rem 2rem",
            color: "#fff"
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                maxWidth: "1320px",
                margin: "0 auto"
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        marginBottom: "3rem"
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            style: {
                                fontSize: "2.5rem",
                                fontWeight: "700",
                                color: "#fff",
                                margin: 0
                            },
                            children: "Blog"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 262,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                            href: "#",
                            style: {
                                color: "#1de9b6",
                                textDecoration: "none",
                                fontSize: "1.1rem",
                                fontWeight: "500",
                                display: "flex",
                                alignItems: "center",
                                gap: "0.5rem"
                            },
                            children: "View All →"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 270,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                    lineNumber: 256,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    style: {
                        display: "grid",
                        gridTemplateColumns: "repeat(auto-fit, minmax(350px, 1fr))",
                        gap: "2rem"
                    },
                    children: blogPosts.map((post)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("article", {
                            style: {
                                background: "#1a1b1e",
                                borderRadius: "16px",
                                overflow: "hidden",
                                border: "1px solid #2a2d31",
                                transition: "all 0.3s ease",
                                cursor: "pointer"
                            },
                            onMouseEnter: (e)=>{
                                e.currentTarget.style.transform = "translateY(-4px)";
                                e.currentTarget.style.boxShadow = "0 12px 40px rgba(0, 0, 0, 0.3)";
                            },
                            onMouseLeave: (e)=>{
                                e.currentTarget.style.transform = "translateY(0)";
                                e.currentTarget.style.boxShadow = "none";
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        height: "200px",
                                        background: post.gradient,
                                        position: "relative",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "center"
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                width: "80px",
                                                height: "80px",
                                                background: "rgba(255, 255, 255, 0.2)",
                                                borderRadius: "12px",
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                backdropFilter: "blur(10px)"
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                style: {
                                                    fontSize: "2rem",
                                                    color: "#fff"
                                                },
                                                children: post.category === "Automation" ? "🔧" : post.category === "Low-code" ? "⚡" : "🚀"
                                            }, void 0, false, {
                                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                                lineNumber: 323,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                            lineNumber: 313,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            style: {
                                                position: "absolute",
                                                top: "1rem",
                                                left: "1rem",
                                                background: "rgba(0, 0, 0, 0.3)",
                                                color: "#fff",
                                                padding: "0.5rem 1rem",
                                                borderRadius: "20px",
                                                fontSize: "0.85rem",
                                                fontWeight: "500",
                                                backdropFilter: "blur(10px)"
                                            },
                                            children: post.category
                                        }, void 0, false, {
                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                            lineNumber: 331,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 305,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    style: {
                                        padding: "2rem"
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            style: {
                                                fontSize: "1.5rem",
                                                fontWeight: "600",
                                                color: "#fff",
                                                marginBottom: "1rem",
                                                lineHeight: "1.4"
                                            },
                                            children: post.title
                                        }, void 0, false, {
                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                            lineNumber: 350,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            style: {
                                                color: "#94a3b8",
                                                lineHeight: "1.6",
                                                marginBottom: "2rem",
                                                fontSize: "1rem"
                                            },
                                            children: post.description
                                        }, void 0, false, {
                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                            lineNumber: 360,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            style: {
                                                display: "flex",
                                                alignItems: "center",
                                                gap: "1rem"
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    style: {
                                                        width: "40px",
                                                        height: "40px",
                                                        borderRadius: "50%",
                                                        background: "linear-gradient(135deg, #1de9b6, #00bfa5)",
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        color: "#000",
                                                        fontWeight: "600",
                                                        fontSize: "1.1rem"
                                                    },
                                                    children: post.author.charAt(0)
                                                }, void 0, false, {
                                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                                    lineNumber: 374,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                color: "#fff",
                                                                fontWeight: "500",
                                                                fontSize: "0.95rem"
                                                            },
                                                            children: post.author
                                                        }, void 0, false, {
                                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                                            lineNumber: 389,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                color: "#64748b",
                                                                fontSize: "0.85rem"
                                                            },
                                                            children: [
                                                                post.date,
                                                                " • ",
                                                                post.readTime
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                                            lineNumber: 396,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                                    lineNumber: 388,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                            lineNumber: 369,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 347,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, post.id, true, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 289,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                    lineNumber: 283,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
            lineNumber: 252,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
        lineNumber: 247,
        columnNumber: 5
    }, this);
}
function Footer() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        style: {
            background: "#1a1b1e",
            padding: "4rem 2rem 2rem",
            color: "#fff",
            borderTop: "1px solid #2a2d31"
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            style: {
                maxWidth: "1200px",
                margin: "0 auto",
                display: "grid",
                gridTemplateColumns: "2fr 1fr 1fr 1fr",
                gap: "3rem"
            },
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                color: "#1de9b6",
                                fontSize: "1.5rem",
                                fontWeight: "bold",
                                border: "2px solid #1de9b6",
                                padding: "0.5rem 1rem",
                                display: "inline-block",
                                marginBottom: "1rem",
                                borderRadius: "8px",
                                background: "rgba(29, 233, 182, 0.1)"
                            },
                            children: "Second HQ"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 430,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            style: {
                                color: "#888",
                                marginBottom: "2rem",
                                lineHeight: "1.6"
                            },
                            children: "Global Capability Centers directory for India"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 443,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                display: "flex",
                                gap: "0.5rem"
                            },
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "email",
                                    placeholder: "Your email address",
                                    style: {
                                        padding: "0.75rem",
                                        border: "none",
                                        borderRadius: "4px",
                                        background: "#3a3d41",
                                        color: "#fff",
                                        flex: 1,
                                        outline: "none"
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 451,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    style: {
                                        background: "linear-gradient(135deg, #1de9b6, #00bfa5)",
                                        color: "#000",
                                        border: "none",
                                        borderRadius: "8px",
                                        padding: "0.75rem 1.5rem",
                                        fontWeight: "600",
                                        cursor: "pointer",
                                        boxShadow: "0 4px 12px rgba(29, 233, 182, 0.3)"
                                    },
                                    children: "Subscribe"
                                }, void 0, false, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 464,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 450,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                    lineNumber: 429,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            style: {
                                color: "#fff",
                                marginBottom: "1.5rem",
                                fontSize: "1.1rem"
                            },
                            children: "Navigation"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 481,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            style: {
                                listStyle: "none",
                                padding: 0,
                                margin: 0
                            },
                            children: [
                                "Home",
                                "Inside India's GCCs",
                                "By Industry",
                                "By Function",
                                "City",
                                "AI Readiness",
                                "Size/Scale",
                                "Culture",
                                "About",
                                "Contact",
                                "Submit GCC"
                            ].map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    style: {
                                        marginBottom: "0.75rem"
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "#",
                                        style: {
                                            color: "#888",
                                            textDecoration: "none",
                                            transition: "color 0.2s"
                                        },
                                        children: item
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 498,
                                        columnNumber: 17
                                    }, this)
                                }, item, false, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 497,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 488,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                    lineNumber: 480,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            style: {
                                color: "#fff",
                                marginBottom: "1.5rem",
                                fontSize: "1.1rem"
                            },
                            children: "Top Industries"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 512,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            style: {
                                listStyle: "none",
                                padding: 0,
                                margin: 0
                            },
                            children: [
                                "Banking & Financial Services",
                                "Technology & IT Services",
                                "Healthcare",
                                "Fintech",
                                "Insurance",
                                "Retail & CPG",
                                "Manufacturing",
                                "Energy",
                                "Entertainment",
                                "Logistics & Supply Chain",
                                "EdTech"
                            ].map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    style: {
                                        marginBottom: "0.75rem"
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "#",
                                        style: {
                                            color: "#888",
                                            textDecoration: "none",
                                            transition: "color 0.2s"
                                        },
                                        children: item
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 530,
                                        columnNumber: 17
                                    }, this)
                                }, item, false, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 529,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 519,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                    lineNumber: 511,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            style: {
                                color: "#fff",
                                marginBottom: "1.5rem",
                                fontSize: "1.1rem"
                            },
                            children: "Top Cities"
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 544,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            style: {
                                listStyle: "none",
                                padding: 0,
                                margin: 0
                            },
                            children: [
                                {
                                    name: "Bengaluru",
                                    icon: "🌆"
                                },
                                {
                                    name: "Hyderabad",
                                    icon: "🌆"
                                },
                                {
                                    name: "Pune",
                                    icon: "🌆"
                                },
                                {
                                    name: "Chennai",
                                    icon: "�"
                                },
                                {
                                    name: "Gurugram",
                                    icon: "🌆"
                                },
                                {
                                    name: "Noida",
                                    icon: "🌆"
                                },
                                {
                                    name: "Mumbai",
                                    icon: "🌆"
                                },
                                {
                                    name: "Ahmedabad",
                                    icon: "🌆"
                                },
                                {
                                    name: "Coimbatore",
                                    icon: "🌆"
                                },
                                {
                                    name: "Kochi",
                                    icon: "�"
                                }
                            ].map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    style: {
                                        marginBottom: "0.75rem"
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "#",
                                        style: {
                                            color: "#888",
                                            textDecoration: "none",
                                            display: "flex",
                                            alignItems: "center",
                                            gap: "0.5rem"
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: item.icon
                                            }, void 0, false, {
                                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                                lineNumber: 576,
                                                columnNumber: 19
                                            }, this),
                                            item.name
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 569,
                                        columnNumber: 17
                                    }, this)
                                }, item.name, false, {
                                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                    lineNumber: 568,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 551,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                    lineNumber: 543,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
            lineNumber: 421,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
        lineNumber: 415,
        columnNumber: 5
    }, this);
}
function NewsletterSection() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        style: {
            position: "relative",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
            padding: "6rem 2rem",
            overflow: "hidden"
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    opacity: 0.2
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            top: "15%",
                            right: "8%",
                            width: "140px",
                            height: "140px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))",
                            transform: "rotate(45deg) perspective(800px) rotateX(30deg) rotateY(30deg)",
                            borderRadius: "10px",
                            border: "1px solid rgba(255,255,255,0.1)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 606,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            bottom: "20%",
                            left: "10%",
                            width: "100px",
                            height: "100px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))",
                            transform: "rotate(-25deg) perspective(600px) rotateX(-20deg) rotateY(40deg)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255,255,255,0.08)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 619,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            top: "25%",
                            left: "15%",
                            width: "60px",
                            height: "60px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))",
                            transform: "rotate(60deg) perspective(500px) rotateX(50deg) rotateY(-25deg)",
                            borderRadius: "6px",
                            border: "1px solid rgba(255,255,255,0.06)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 632,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            top: "10%",
                            left: "75%",
                            width: "80px",
                            height: "80px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.07), rgba(255,255,255,0.02))",
                            transform: "rotate(-40deg) perspective(600px) rotateX(-35deg) rotateY(55deg)",
                            borderRadius: "7px",
                            border: "1px solid rgba(255,255,255,0.07)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 644,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            bottom: "15%",
                            right: "20%",
                            width: "90px",
                            height: "90px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.05), rgba(255,255,255,0.01))",
                            transform: "rotate(20deg) perspective(700px) rotateX(25deg) rotateY(-45deg)",
                            borderRadius: "8px",
                            border: "1px solid rgba(255,255,255,0.05)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 656,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            top: "60%",
                            left: "5%",
                            width: "50px",
                            height: "50px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.04), rgba(255,255,255,0.01))",
                            transform: "rotate(75deg) perspective(400px) rotateX(60deg) rotateY(-15deg)",
                            borderRadius: "5px",
                            border: "1px solid rgba(255,255,255,0.04)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 669,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            position: "absolute",
                            top: "40%",
                            right: "5%",
                            width: "70px",
                            height: "70px",
                            background: "linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))",
                            transform: "rotate(-60deg) perspective(500px) rotateX(-40deg) rotateY(30deg)",
                            borderRadius: "6px",
                            border: "1px solid rgba(255,255,255,0.06)"
                        }
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 681,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 597,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    position: "relative",
                    zIndex: 10,
                    maxWidth: "600px",
                    margin: "0 auto",
                    textAlign: "center"
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        style: {
                            color: "#ffffff",
                            fontWeight: 700,
                            fontSize: "2.5rem",
                            marginBottom: "1rem",
                            lineHeight: "1.2",
                            textShadow: "0 2px 4px rgba(0,0,0,0.1)"
                        },
                        children: "All the best resources in one place"
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 702,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        style: {
                            color: "rgba(255,255,255,0.9)",
                            fontSize: "1.1rem",
                            marginBottom: "2.5rem",
                            lineHeight: "1.6"
                        },
                        children: "Improve & automate your work with the best no-code tools. Subscribe to get new resources weekly."
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 713,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            display: "flex",
                            justifyContent: "center",
                            gap: "0",
                            maxWidth: "450px",
                            margin: "0 auto"
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "email",
                                placeholder: "Your email address",
                                style: {
                                    padding: "1rem 1.5rem",
                                    fontSize: "1rem",
                                    borderRadius: "8px 0 0 8px",
                                    outline: "none",
                                    flex: 1,
                                    background: "rgba(0,0,0,0.4)",
                                    color: "#fff",
                                    backdropFilter: "blur(10px)",
                                    border: "1px solid rgba(255,255,255,0.2)",
                                    minHeight: "50px"
                                }
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 730,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                style: {
                                    background: "#1de9b6",
                                    color: "#000",
                                    border: "none",
                                    borderRadius: "0 8px 8px 0",
                                    padding: "1rem 2rem",
                                    fontWeight: 600,
                                    fontSize: "1rem",
                                    cursor: "pointer",
                                    transition: "all 0.3s ease",
                                    textTransform: "uppercase",
                                    letterSpacing: "0.5px",
                                    minHeight: "50px"
                                },
                                children: "Subscribe"
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 746,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 723,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 695,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
        lineNumber: 590,
        columnNumber: 5
    }, this);
}
function Home() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        style: {
            minHeight: "100vh",
            width: "100vw",
            background: "#ffffff"
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$app$2f$TopNavbar$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].heroSection,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(TopNavbar, {}, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 778,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            padding: "4rem 2rem",
                            textAlign: "center",
                            maxWidth: "800px",
                            margin: "0 auto"
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                style: {
                                    color: "#111827",
                                    fontWeight: 700,
                                    fontSize: "3rem",
                                    lineHeight: "1.2",
                                    marginBottom: "1.5rem"
                                },
                                children: [
                                    "Every ",
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            color: "#16d1aa"
                                        },
                                        children: "Global Capability Center (GCC)"
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 781,
                                        columnNumber: 19
                                    }, this),
                                    " in India."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 780,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                style: {
                                    color: "#374151",
                                    fontFamily: "sans-serif",
                                    fontSize: "1.25rem",
                                    margin: "0 0 3rem 0",
                                    lineHeight: "1.6"
                                },
                                children: "Documented & Decoded. Explore the work, talent, AI-readiness, culture, and employer brand salience of every global enterprise's (un)official second HQ: their India Global Capability Center."
                            }, void 0, false, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 783,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                style: {
                                    display: "flex",
                                    justifyContent: "center",
                                    gap: "0",
                                    maxWidth: "500px",
                                    margin: "0 auto"
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "email",
                                        placeholder: "Your email address",
                                        style: {
                                            padding: "1rem 1.5rem",
                                            fontSize: "1rem",
                                            border: "1px solid #d1d5db",
                                            borderRadius: "8px 0 0 8px",
                                            outline: "none",
                                            flex: 1,
                                            background: "#ffffff",
                                            color: "#374151",
                                            minHeight: "50px"
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 795,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        style: {
                                            background: "#16d1aa",
                                            color: "#ffffff",
                                            border: "none",
                                            borderRadius: "0 8px 8px 0",
                                            padding: "1rem 2rem",
                                            fontWeight: 600,
                                            fontSize: "1rem",
                                            cursor: "pointer",
                                            minHeight: "50px",
                                            transition: "background 0.2s ease"
                                        },
                                        children: "Subscribe"
                                    }, void 0, false, {
                                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                        lineNumber: 810,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                                lineNumber: 786,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 779,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        width: "100%",
                        height: "120",
                        viewBox: "0 0 1440 120",
                        style: {
                            display: "block",
                            position: "absolute",
                            bottom: 0,
                            left: 0,
                            zIndex: 2
                        },
                        preserveAspectRatio: "none",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            fill: "#0a0b0d",
                            d: "   M0,0   Q720,160 1440,0   L1440,120   L0,120   Z   "
                        }, void 0, false, {
                            fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                            lineNumber: 841,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                        lineNumber: 828,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 777,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ResourceSection, {}, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 853,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(BlogSection, {}, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 854,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(NewsletterSection, {}, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 855,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$GitHub$2f$second$2d$hq$2f$second$2d$hq$2d$frontend$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Footer, {}, void 0, false, {
                fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
                lineNumber: 856,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx",
        lineNumber: 770,
        columnNumber: 5
    }, this);
}
}),
"[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else //TURBOPACK unreachable
            ;
        } else //TURBOPACK unreachable
        ;
    }
} //# sourceMappingURL=module.compiled.js.map
}),
"[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

module.exports = __turbopack_context__.r("[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}),
"[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)", ((__turbopack_context__, module, exports) => {
"use strict";

module.exports = __turbopack_context__.r("[project]/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__520322a5._.js.map