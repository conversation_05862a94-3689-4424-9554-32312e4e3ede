{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/Documents/GitHub/second-hq/second-hq-frontend/app/TopNavbar.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"TopNavbar-module__RqcZ4q__active\",\n  \"heroSection\": \"TopNavbar-module__RqcZ4q__heroSection\",\n  \"links\": \"TopNavbar-module__RqcZ4q__links\",\n  \"logo\": \"TopNavbar-module__RqcZ4q__logo\",\n  \"navLink\": \"TopNavbar-module__RqcZ4q__navLink\",\n  \"navbar\": \"TopNavbar-module__RqcZ4q__navbar\",\n  \"subscribeBtn\": \"TopNavbar-module__RqcZ4q__subscribeBtn\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/Documents/GitHub/second-hq/second-hq-frontend/app/ResourceSection.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"active\": \"ResourceSection-module__fanLqG__active\",\n  \"card\": \"ResourceSection-module__fanLqG__card\",\n  \"cardDesc\": \"ResourceSection-module__fanLqG__cardDesc\",\n  \"cardIcon\": \"ResourceSection-module__fanLqG__cardIcon\",\n  \"cardLink\": \"ResourceSection-module__fanLqG__cardLink\",\n  \"cardTitle\": \"ResourceSection-module__fanLqG__cardTitle\",\n  \"categoryBadge\": \"ResourceSection-module__fanLqG__categoryBadge\",\n  \"fadeInUp\": \"ResourceSection-module__fanLqG__fadeInUp\",\n  \"grid\": \"ResourceSection-module__fanLqG__grid\",\n  \"sectionWrapper\": \"ResourceSection-module__fanLqG__sectionWrapper\",\n  \"sidebar\": \"ResourceSection-module__fanLqG__sidebar\",\n  \"sidebarButton\": \"ResourceSection-module__fanLqG__sidebarButton\",\n  \"sidebarCategory\": \"ResourceSection-module__fanLqG__sidebarCategory\",\n  \"sidebarCount\": \"ResourceSection-module__fanLqG__sidebarCount\",\n  \"subDropdown\": \"ResourceSection-module__fanLqG__subDropdown\",\n  \"subDropdownItem\": \"ResourceSection-module__fanLqG__subDropdownItem\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/second-hq/second-hq-frontend/app/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport styles from \"./TopNavbar.module.css\";\r\nimport resourceStyles from \"./ResourceSection.module.css\";\r\n\r\nconst navLinks = [\r\n    { name: \"Home\", href: \"#\" },\r\n    { name: \"Inside India's GCCs\", href: \"#\" },\r\n    { name: \"By Industry\", href: \"#\" },\r\n    { name: \"By Function\", href: \"#\" },\r\n    { name: \"City\", href: \"#\" },\r\n    { name: \"AI Readiness\", href: \"#\" },\r\n    { name: \"Size/Scale\", href: \"#\" },\r\n    { name: \"Culture\", href: \"#\" },\r\n];\r\n\r\nfunction TopNavbar() {\r\n    return (\r\n        <nav className={styles.navbar}>\r\n            <div className={styles.logo}>Second HQ</div>\r\n            <div className={styles.links}>\r\n                {navLinks.map((link) => (\r\n                    <a\r\n                        key={link.name}\r\n                        href={link.href}\r\n                        className={`${styles.navLink} ${link.name === \"Home\" ? styles.active : \"\"}`}\r\n                    >\r\n                        {link.name}\r\n                    </a>\r\n                ))}\r\n            </div>\r\n            <button className={styles.subscribeBtn}>Subscribe</button>\r\n        </nav>\r\n    );\r\n}\r\n\r\nconst categories = [\r\n  { name: \"All GCCs\",  },\r\n  { name: \"By Industry\", sub: [\"Banking & Financial Services (BFSI)\", \"Fintech\", \"Insurance\", \"Healthcare\", \"Pharma & MedTech\", \"Retail & CPG\", \"Energy\", \"Manufacturing\", \"Entertainment\", \"Travel & Hospitality\", \"Logistics & Supply Chain\", \"Enterprise SaaS\", \"Cloud & Infra\", \"Cybersecurity\", \"Gaming\", \"EdTech\", \"Public Sector\", \"Technology & IT Services\", \"Professional & Business Services\", \"Automotive & Industrial\"] },\r\n  { name: \"By Function\",  sub: [\"Engineering Hub\", \"Product & Design Studio\", \"Data/ML Center\", \"R&D Lab\", \"Risk & Compliance COE\", \"Finance Shared Services\", \"Customer Ops\", \"Supply Chain COE\", \"Security Ops Center\", \"Innovation Garage\", \"Platform/Infra Hub\"] },\r\n  { name: \"City\",  sub: [\"Bengaluru\", \"Hyderabad\", \"Pune\", \"Chennai\", \"Gurugram\", \"Noida\", \"Mumbai\", \"Ahmedabad\", \"Coimbatore\", \"Kochi\", \"Jaipur\", \"Chandigarh\"] },\r\n  { name: \"AI Readiness\", sub: [\"AI-Native\", \"AI Platform/CoE\", \"Data-Driven\", \"AI-Adopting\", \"Legacy-Modernizing\", \"Regulated-AI\", \"Early Stage-AI\"] },\r\n  { name: \"Size/Scale\",  sub: [\"0-200 Employees\", \"200-500 Employees\", \"500-1,500 Employees\", \"1,500-3,000 Employees\", \"3,000+ Employees\"] },\r\n  { name: \"Culture\",  sub: [\"Builder Culture\", \"Global Ownership\", \"High Experimentation\", \"Strong Learning\", \"Hybrid-First\", \"Inclusion Leader\", \"Low-Hierarchy\", \"Process-Driven\", \"Customer-Obsessed\"] },\r\n];\r\n\r\ntype GCC = {\r\n  title: string;\r\n  desc: string;\r\n  icon: string;\r\n  category: string;\r\n  link: string;\r\n  tags?: string[];\r\n};\r\n\r\nconst gccs: GCC[] = [\r\n  {\r\n    title: \"Microsoft India Development Center\",\r\n    desc: \"One of Microsoft's largest R&D centers outside the US, focusing on cloud, AI, and enterprise solutions with over 8,000 employees.\",\r\n    icon: \"�\",\r\n    category: \"By Industry\",\r\n    link: \"#\",\r\n    tags: [\"Technology & IT Services\", \"Hyderabad\"],\r\n  },\r\n  {\r\n    title: \"Goldman Sachs Bengaluru\",\r\n    desc: \"Major technology and operations hub for Goldman Sachs, handling global trading systems, risk management, and digital innovation.\",\r\n    icon: \"🏦\",\r\n    category: \"By Industry\",\r\n    link: \"#\",\r\n    tags: [\"Banking & Financial Services (BFSI)\", \"Bengaluru\"],\r\n  },\r\n  {\r\n    title: \"Google India\",\r\n    desc: \"Google's largest engineering center outside the US, working on products like Google Pay, Search, and Android with AI-first approach.\",\r\n    icon: \"🔍\",\r\n    category: \"By Function\",\r\n    link: \"#\",\r\n    tags: [\"Engineering Hub\", \"Bengaluru\"],\r\n  },\r\n  {\r\n    title: \"Amazon Development Center\",\r\n    desc: \"Amazon's largest software development center outside Seattle, focusing on AWS, Alexa, and e-commerce platforms.\",\r\n    icon: \"�\",\r\n    category: \"City\",\r\n    link: \"#\",\r\n    tags: [\"Hyderabad\", \"Bengaluru\"],\r\n  },\r\n  {\r\n    title: \"Walmart Global Tech\",\r\n    desc: \"Walmart's technology hub driving digital transformation with focus on e-commerce, supply chain, and data analytics.\",\r\n    icon: \"�\",\r\n    category: \"AI Readiness\",\r\n    link: \"#\",\r\n    tags: [\"AI Platform/CoE\", \"Bengaluru\"],\r\n  },\r\n  {\r\n    title: \"JPMorgan Chase Technology Center\",\r\n    desc: \"Global technology center focusing on digital banking, blockchain, and financial technology innovations.\",\r\n    icon: \"�\",\r\n    category: \"Size/Scale\",\r\n    link: \"#\",\r\n    tags: [\"Large (1001-5000)\", \"Mumbai\"],\r\n  },\r\n];\r\n\r\nfunction ResourceSection() {\r\n  const [selected, setSelected] = useState(\"All GCCs\");\r\n  const [openDropdown, setOpenDropdown] = useState<string | null>(null);\r\n  const [selectedSub, setSelectedSub] = useState<string | null>(null);\r\n\r\n  const filteredGCCs =\r\n    selected === \"All GCCs\"\r\n      ? gccs\r\n      : selectedSub\r\n      ? gccs.filter((g) => g.category === selected && g.tags?.includes(selectedSub))\r\n      : gccs.filter((g) => g.category === selected);\r\n\r\n  return (\r\n    <div className={resourceStyles.sectionWrapper}>\r\n      <aside className={resourceStyles.sidebar}>\r\n        {categories.map((cat) => (\r\n          <div key={cat.name}>\r\n            <button\r\n              className={`${resourceStyles.sidebarButton} ${\r\n                selected === cat.name ? resourceStyles.active : \"\"\r\n              }`}\r\n              onClick={() => {\r\n                setSelected(cat.name);\r\n                setSelectedSub(null);\r\n                // Only open dropdown if not \"All GCCs\"\r\n                if (cat.name !== \"All GCCs\") {\r\n                  setOpenDropdown(openDropdown === cat.name ? null : cat.name);\r\n                }\r\n              }}\r\n              style={{\r\n                color: selected === cat.name ? \"#1de9b6\" : undefined,\r\n                fontWeight: selected === cat.name ? 600 : undefined,\r\n                justifyContent: \"flex-start\",\r\n                width: \"100%\",\r\n              }}\r\n            >\r\n              {/* <span>{cat.icon}</span> */}\r\n              {cat.name}\r\n              {cat.name === \"All GCCs\" && (\r\n                <span className={resourceStyles.sidebarCount}>{gccs.length}</span>\r\n              )}\r\n              {/* Arrow only for categories except All GCCs */}\r\n              {cat.name !== \"All GCCs\" && (\r\n                <span style={{ marginLeft: \"auto\", fontSize: 18 }}>\r\n                  {openDropdown === cat.name ? \"▲\" : \"▼\"}\r\n                </span>\r\n              )}\r\n            </button>\r\n            {/* Dropdown only for categories except All GCCs */}\r\n            {cat.name !== \"All GCCs\" && openDropdown === cat.name && (\r\n              <div className={resourceStyles.subDropdown}>\r\n                {cat.sub && cat.sub.length > 0 ? (\r\n                  cat.sub.map((sub) => (\r\n                    <div\r\n                      key={sub}\r\n                      className={`${resourceStyles.subDropdownItem} ${\r\n                        selectedSub === sub ? resourceStyles.active : \"\"\r\n                      }`}\r\n                      onClick={() => {\r\n                        setSelected(cat.name);\r\n                        setSelectedSub(sub);\r\n                      }}\r\n                    >\r\n                      {sub}\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <div\r\n                    className={resourceStyles.subDropdownItem}\r\n                    style={{ color: \"#888\", fontStyle: \"italic\" }}\r\n                  >\r\n                    No subcategories\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </aside>\r\n      <div className={resourceStyles.grid}>\r\n        {filteredGCCs.map((gcc) => (\r\n          <div key={gcc.title} className={resourceStyles.card}>\r\n            <div className={resourceStyles.cardIcon}>{gcc.icon}</div>\r\n            <div className={resourceStyles.cardTitle}>{gcc.title}</div>\r\n            <div className={resourceStyles.cardDesc}>{gcc.desc}</div>\r\n            <a\r\n              href={gcc.link}\r\n              className={resourceStyles.cardLink}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              title=\"Open\"\r\n            >\r\n              ↗\r\n            </a>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction BlogSection() {\r\n  const blogPosts = [\r\n    {\r\n      id: 1,\r\n      category: \"Automation\",\r\n      title: \"The best no-code automation tools to grow your startup\",\r\n      description: \"No-code automation is gaining momentum, as businesses realize how easy it can be. You can use no-code automation tools to quickly develop scripts and other applications.\",\r\n      author: \"Andy\",\r\n      date: \"Nov 23, 2022\",\r\n      readTime: \"5 min read\",\r\n      image: \"https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=300&fit=crop&crop=center\",\r\n      gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\r\n    },\r\n    {\r\n      id: 2,\r\n      category: \"Low-code\",\r\n      title: \"No code vs low code key differences for developers\",\r\n      description: \"With the no-code movement taking shape fast, it's time to refocus. The future will be less about helping people build stuff and more about helping people build demand for stuff.\",\r\n      author: \"Andy\",\r\n      date: \"Nov 22, 2022\",\r\n      readTime: \"5 min read\",\r\n      image: \"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=center\",\r\n      gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\r\n    },\r\n    {\r\n      id: 3,\r\n      category: \"Tech\",\r\n      title: \"Guide to building your own no-code tech stack\",\r\n      description: \"Once you have a goal in mind, the idea is to \\\"stack\\\" technology tools that will work toward that goal. There are essentially limitless ways to do this, but keeping it simple is the best.\",\r\n      author: \"Erin Hannon\",\r\n      date: \"Nov 21, 2022\",\r\n      readTime: \"5 min read\",\r\n      image: \"https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400&h=300&fit=crop&crop=center\",\r\n      gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section style={{\r\n      background: \"#0a0b0d\",\r\n      padding: \"5rem 2rem\",\r\n      color: \"#fff\"\r\n    }}>\r\n      <div style={{\r\n        maxWidth: \"1320px\",\r\n        margin: \"0 auto\"\r\n      }}>\r\n        <div style={{\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          justifyContent: \"space-between\",\r\n          marginBottom: \"3rem\"\r\n        }}>\r\n          <h2 style={{\r\n            fontSize: \"2.5rem\",\r\n            fontWeight: \"700\",\r\n            color: \"#fff\",\r\n            margin: 0\r\n          }}>\r\n            Blog\r\n          </h2>\r\n          <a href=\"#\" style={{\r\n            color: \"#1de9b6\",\r\n            textDecoration: \"none\",\r\n            fontSize: \"1.1rem\",\r\n            fontWeight: \"500\",\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: \"0.5rem\"\r\n          }}>\r\n            View All →\r\n          </a>\r\n        </div>\r\n\r\n        <div style={{\r\n          display: \"grid\",\r\n          gridTemplateColumns: \"repeat(auto-fit, minmax(350px, 1fr))\",\r\n          gap: \"2rem\"\r\n        }}>\r\n          {blogPosts.map((post) => (\r\n            <article key={post.id} style={{\r\n              background: \"#1a1b1e\",\r\n              borderRadius: \"16px\",\r\n              overflow: \"hidden\",\r\n              border: \"1px solid #2a2d31\",\r\n              transition: \"all 0.3s ease\",\r\n              cursor: \"pointer\"\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              e.currentTarget.style.transform = \"translateY(-4px)\";\r\n              e.currentTarget.style.boxShadow = \"0 12px 40px rgba(0, 0, 0, 0.3)\";\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.currentTarget.style.transform = \"translateY(0)\";\r\n              e.currentTarget.style.boxShadow = \"none\";\r\n            }}>\r\n              <div style={{\r\n                height: \"200px\",\r\n                background: post.gradient,\r\n                position: \"relative\",\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"center\"\r\n              }}>\r\n                <div style={{\r\n                  width: \"80px\",\r\n                  height: \"80px\",\r\n                  background: \"rgba(255, 255, 255, 0.2)\",\r\n                  borderRadius: \"12px\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  justifyContent: \"center\",\r\n                  backdropFilter: \"blur(10px)\"\r\n                }}>\r\n                  <span style={{\r\n                    fontSize: \"2rem\",\r\n                    color: \"#fff\"\r\n                  }}>\r\n                    {post.category === \"Automation\" ? \"🔧\" :\r\n                     post.category === \"Low-code\" ? \"⚡\" : \"🚀\"}\r\n                  </span>\r\n                </div>\r\n                <span style={{\r\n                  position: \"absolute\",\r\n                  top: \"1rem\",\r\n                  left: \"1rem\",\r\n                  background: \"rgba(0, 0, 0, 0.3)\",\r\n                  color: \"#fff\",\r\n                  padding: \"0.5rem 1rem\",\r\n                  borderRadius: \"20px\",\r\n                  fontSize: \"0.85rem\",\r\n                  fontWeight: \"500\",\r\n                  backdropFilter: \"blur(10px)\"\r\n                }}>\r\n                  {post.category}\r\n                </span>\r\n              </div>\r\n\r\n              <div style={{\r\n                padding: \"2rem\"\r\n              }}>\r\n                <h3 style={{\r\n                  fontSize: \"1.5rem\",\r\n                  fontWeight: \"600\",\r\n                  color: \"#fff\",\r\n                  marginBottom: \"1rem\",\r\n                  lineHeight: \"1.4\"\r\n                }}>\r\n                  {post.title}\r\n                </h3>\r\n\r\n                <p style={{\r\n                  color: \"#94a3b8\",\r\n                  lineHeight: \"1.6\",\r\n                  marginBottom: \"2rem\",\r\n                  fontSize: \"1rem\"\r\n                }}>\r\n                  {post.description}\r\n                </p>\r\n\r\n                <div style={{\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"1rem\"\r\n                }}>\r\n                  <div style={{\r\n                    width: \"40px\",\r\n                    height: \"40px\",\r\n                    borderRadius: \"50%\",\r\n                    background: \"linear-gradient(135deg, #1de9b6, #00bfa5)\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    justifyContent: \"center\",\r\n                    color: \"#000\",\r\n                    fontWeight: \"600\",\r\n                    fontSize: \"1.1rem\"\r\n                  }}>\r\n                    {post.author.charAt(0)}\r\n                  </div>\r\n                  <div>\r\n                    <div style={{\r\n                      color: \"#fff\",\r\n                      fontWeight: \"500\",\r\n                      fontSize: \"0.95rem\"\r\n                    }}>\r\n                      {post.author}\r\n                    </div>\r\n                    <div style={{\r\n                      color: \"#64748b\",\r\n                      fontSize: \"0.85rem\"\r\n                    }}>\r\n                      {post.date} • {post.readTime}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </article>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction Footer() {\r\n  return (\r\n    <footer style={{\r\n      background: \"#1a1b1e\",\r\n      padding: \"4rem 2rem 2rem\",\r\n      color: \"#fff\",\r\n      borderTop: \"1px solid #2a2d31\"\r\n    }}>\r\n      <div style={{\r\n        maxWidth: \"1200px\",\r\n        margin: \"0 auto\",\r\n        display: \"grid\",\r\n        gridTemplateColumns: \"2fr 1fr 1fr 1fr\",\r\n        gap: \"3rem\"\r\n      }}>\r\n        {/* Brand Section */}\r\n        <div>\r\n          <div style={{\r\n            color: \"#1de9b6\",\r\n            fontSize: \"1.5rem\",\r\n            fontWeight: \"bold\",\r\n            border: \"2px solid #1de9b6\",\r\n            padding: \"0.5rem 1rem\",\r\n            display: \"inline-block\",\r\n            marginBottom: \"1rem\",\r\n            borderRadius: \"8px\",\r\n            background: \"rgba(29, 233, 182, 0.1)\"\r\n          }}>\r\n            Second HQ\r\n          </div>\r\n          <p style={{\r\n            color: \"#888\",\r\n            marginBottom: \"2rem\",\r\n            lineHeight: \"1.6\"\r\n          }}>\r\n            Global Capability Centers directory for India\r\n          </p>\r\n          <div style={{ display: \"flex\", gap: \"0.5rem\" }}>\r\n            <input\r\n              type=\"email\"\r\n              placeholder=\"Your email address\"\r\n              style={{\r\n                padding: \"0.75rem\",\r\n                border: \"none\",\r\n                borderRadius: \"4px\",\r\n                background: \"#3a3d41\",\r\n                color: \"#fff\",\r\n                flex: 1,\r\n                outline: \"none\"\r\n              }}\r\n            />\r\n            <button style={{\r\n              background: \"linear-gradient(135deg, #1de9b6, #00bfa5)\",\r\n              color: \"#000\",\r\n              border: \"none\",\r\n              borderRadius: \"8px\",\r\n              padding: \"0.75rem 1.5rem\",\r\n              fontWeight: \"600\",\r\n              cursor: \"pointer\",\r\n              boxShadow: \"0 4px 12px rgba(29, 233, 182, 0.3)\"\r\n            }}>\r\n              Subscribe\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation Section */}\r\n        <div>\r\n          <h3 style={{\r\n            color: \"#fff\",\r\n            marginBottom: \"1.5rem\",\r\n            fontSize: \"1.1rem\"\r\n          }}>\r\n            Navigation\r\n          </h3>\r\n          <ul style={{\r\n            listStyle: \"none\",\r\n            padding: 0,\r\n            margin: 0\r\n          }}>\r\n            {[\r\n              \"Home\", \"Inside India's GCCs\", \"By Industry\", \"By Function\", \"City\",\r\n              \"AI Readiness\", \"Size/Scale\", \"Culture\", \"About\", \"Contact\", \"Submit GCC\"\r\n            ].map((item) => (\r\n              <li key={item} style={{ marginBottom: \"0.75rem\" }}>\r\n                <a href=\"#\" style={{\r\n                  color: \"#888\",\r\n                  textDecoration: \"none\",\r\n                  transition: \"color 0.2s\"\r\n                }}>\r\n                  {item}\r\n                </a>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n\r\n        {/* Top Industries Section */}\r\n        <div>\r\n          <h3 style={{\r\n            color: \"#fff\",\r\n            marginBottom: \"1.5rem\",\r\n            fontSize: \"1.1rem\"\r\n          }}>\r\n            Top Industries\r\n          </h3>\r\n          <ul style={{\r\n            listStyle: \"none\",\r\n            padding: 0,\r\n            margin: 0\r\n          }}>\r\n            {[\r\n              \"Banking & Financial Services\", \"Technology & IT Services\", \"Healthcare\", \"Fintech\", \"Insurance\",\r\n              \"Retail & CPG\", \"Manufacturing\", \"Energy\", \"Entertainment\",\r\n              \"Logistics & Supply Chain\", \"EdTech\"\r\n            ].map((item) => (\r\n              <li key={item} style={{ marginBottom: \"0.75rem\" }}>\r\n                <a href=\"#\" style={{\r\n                  color: \"#888\",\r\n                  textDecoration: \"none\",\r\n                  transition: \"color 0.2s\"\r\n                }}>\r\n                  {item}\r\n                </a>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n\r\n        {/* Top Cities Section */}\r\n        <div>\r\n          <h3 style={{\r\n            color: \"#fff\",\r\n            marginBottom: \"1.5rem\",\r\n            fontSize: \"1.1rem\"\r\n          }}>\r\n            Top Cities\r\n          </h3>\r\n          <ul style={{\r\n            listStyle: \"none\",\r\n            padding: 0,\r\n            margin: 0\r\n          }}>\r\n            {[\r\n              { name: \"Bengaluru\", icon: \"🌆\" },\r\n              { name: \"Hyderabad\", icon: \"🌆\" },\r\n              { name: \"Pune\", icon: \"🌆\" },\r\n              { name: \"Chennai\", icon: \"�\" },\r\n              { name: \"Gurugram\", icon: \"🌆\" },\r\n              { name: \"Noida\", icon: \"🌆\" },\r\n              { name: \"Mumbai\", icon: \"🌆\" },\r\n              { name: \"Ahmedabad\", icon: \"🌆\" },\r\n              { name: \"Coimbatore\", icon: \"🌆\" },\r\n              { name: \"Kochi\", icon: \"�\" }\r\n            ].map((item) => (\r\n              <li key={item.name} style={{ marginBottom: \"0.75rem\" }}>\r\n                <a href=\"#\" style={{\r\n                  color: \"#888\",\r\n                  textDecoration: \"none\",\r\n                  display: \"flex\",\r\n                  alignItems: \"center\",\r\n                  gap: \"0.5rem\"\r\n                }}>\r\n                  <span>{item.icon}</span>\r\n                  {item.name}\r\n                </a>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n\r\nfunction NewsletterSection() {\r\n  return (\r\n    <section style={{\r\n      position: \"relative\",\r\n      background: \"linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)\",\r\n      padding: \"6rem 2rem\",\r\n      overflow: \"hidden\"\r\n    }}>\r\n      {/* 3D Geometric Shapes Background */}\r\n      <div style={{\r\n        position: \"absolute\",\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0,\r\n        opacity: 0.2\r\n      }}>\r\n        {/* Large cube - top right */}\r\n        <div style={{\r\n          position: \"absolute\",\r\n          top: \"15%\",\r\n          right: \"8%\",\r\n          width: \"140px\",\r\n          height: \"140px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))\",\r\n          transform: \"rotate(45deg) perspective(800px) rotateX(30deg) rotateY(30deg)\",\r\n          borderRadius: \"10px\",\r\n          border: \"1px solid rgba(255,255,255,0.1)\"\r\n        }} />\r\n\r\n        {/* Medium cube - bottom left */}\r\n        <div style={{\r\n          position: \"absolute\",\r\n          bottom: \"20%\",\r\n          left: \"10%\",\r\n          width: \"100px\",\r\n          height: \"100px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))\",\r\n          transform: \"rotate(-25deg) perspective(600px) rotateX(-20deg) rotateY(40deg)\",\r\n          borderRadius: \"8px\",\r\n          border: \"1px solid rgba(255,255,255,0.08)\"\r\n        }} />\r\n\r\n        {/* Small cubes scattered */}\r\n        <div style={{\r\n          position: \"absolute\",\r\n          top: \"25%\",\r\n          left: \"15%\",\r\n          width: \"60px\",\r\n          height: \"60px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))\",\r\n          transform: \"rotate(60deg) perspective(500px) rotateX(50deg) rotateY(-25deg)\",\r\n          borderRadius: \"6px\",\r\n          border: \"1px solid rgba(255,255,255,0.06)\"\r\n        }} />\r\n\r\n        <div style={{\r\n          position: \"absolute\",\r\n          top: \"10%\",\r\n          left: \"75%\",\r\n          width: \"80px\",\r\n          height: \"80px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.07), rgba(255,255,255,0.02))\",\r\n          transform: \"rotate(-40deg) perspective(600px) rotateX(-35deg) rotateY(55deg)\",\r\n          borderRadius: \"7px\",\r\n          border: \"1px solid rgba(255,255,255,0.07)\"\r\n        }} />\r\n\r\n        <div style={{\r\n          position: \"absolute\",\r\n          bottom: \"15%\",\r\n          right: \"20%\",\r\n          width: \"90px\",\r\n          height: \"90px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.05), rgba(255,255,255,0.01))\",\r\n          transform: \"rotate(20deg) perspective(700px) rotateX(25deg) rotateY(-45deg)\",\r\n          borderRadius: \"8px\",\r\n          border: \"1px solid rgba(255,255,255,0.05)\"\r\n        }} />\r\n\r\n        {/* Additional small cubes for more depth */}\r\n        <div style={{\r\n          position: \"absolute\",\r\n          top: \"60%\",\r\n          left: \"5%\",\r\n          width: \"50px\",\r\n          height: \"50px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.04), rgba(255,255,255,0.01))\",\r\n          transform: \"rotate(75deg) perspective(400px) rotateX(60deg) rotateY(-15deg)\",\r\n          borderRadius: \"5px\",\r\n          border: \"1px solid rgba(255,255,255,0.04)\"\r\n        }} />\r\n\r\n        <div style={{\r\n          position: \"absolute\",\r\n          top: \"40%\",\r\n          right: \"5%\",\r\n          width: \"70px\",\r\n          height: \"70px\",\r\n          background: \"linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02))\",\r\n          transform: \"rotate(-60deg) perspective(500px) rotateX(-40deg) rotateY(30deg)\",\r\n          borderRadius: \"6px\",\r\n          border: \"1px solid rgba(255,255,255,0.06)\"\r\n        }} />\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div style={{\r\n        position: \"relative\",\r\n        zIndex: 10,\r\n        maxWidth: \"600px\",\r\n        margin: \"0 auto\",\r\n        textAlign: \"center\"\r\n      }}>\r\n        <h2 style={{\r\n          color: \"#ffffff\",\r\n          fontWeight: 700,\r\n          fontSize: \"2.5rem\",\r\n          marginBottom: \"1rem\",\r\n          lineHeight: \"1.2\",\r\n          textShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\r\n        }}>\r\n          All the best resources in one place\r\n        </h2>\r\n\r\n        <p style={{\r\n          color: \"rgba(255,255,255,0.9)\",\r\n          fontSize: \"1.1rem\",\r\n          marginBottom: \"2.5rem\",\r\n          lineHeight: \"1.6\"\r\n        }}>\r\n          Improve & automate your work with the best no-code tools. Subscribe to get new resources weekly.\r\n        </p>\r\n\r\n        {/* Email Subscription Form */}\r\n        <div style={{\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          gap: \"0\",\r\n          maxWidth: \"450px\",\r\n          margin: \"0 auto\"\r\n        }}>\r\n          <input\r\n            type=\"email\"\r\n            placeholder=\"Your email address\"\r\n            style={{\r\n              padding: \"1rem 1.5rem\",\r\n              fontSize: \"1rem\",\r\n              borderRadius: \"8px 0 0 8px\",\r\n              outline: \"none\",\r\n              flex: 1,\r\n              background: \"rgba(0,0,0,0.4)\",\r\n              color: \"#fff\",\r\n              backdropFilter: \"blur(10px)\",\r\n              border: \"1px solid rgba(255,255,255,0.2)\",\r\n              minHeight: \"50px\"\r\n            }}\r\n          />\r\n          <button style={{\r\n            background: \"#1de9b6\",\r\n            color: \"#000\",\r\n            border: \"none\",\r\n            borderRadius: \"0 8px 8px 0\",\r\n            padding: \"1rem 2rem\",\r\n            fontWeight: 600,\r\n            fontSize: \"1rem\",\r\n            cursor: \"pointer\",\r\n            transition: \"all 0.3s ease\",\r\n            textTransform: \"uppercase\",\r\n            letterSpacing: \"0.5px\",\r\n            minHeight: \"50px\"\r\n          }}>\r\n            Subscribe\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default function Home() {\r\n  return (\r\n    <div\r\n      style={{\r\n        minHeight: \"100vh\",\r\n        width: \"100vw\",\r\n        background: \"#ffffff\",\r\n      }}\r\n    >\r\n      <div className={styles.heroSection}>\r\n        <TopNavbar />\r\n        <div style={{ padding: \"4rem 2rem\", textAlign: \"center\", maxWidth: \"800px\", margin: \"0 auto\" }}>\r\n          <h1 style={{ color: \"#111827\", fontWeight: 700, fontSize: \"3rem\", lineHeight: \"1.2\", marginBottom: \"1.5rem\" }}>\r\n            Every <span style={{ color: \"#16d1aa\" }}>Global Capability Center (GCC)</span> in India.\r\n          </h1>\r\n          <p style={{ color: \"#374151\", fontFamily: \"sans-serif\", fontSize: \"1.25rem\", margin: \"0 0 3rem 0\", lineHeight: \"1.6\" }}>\r\n            Documented & Decoded. Explore the work, talent, AI-readiness, culture, and employer brand salience of every global enterprise's (un)official second HQ: their India Global Capability Center.\r\n          </p>\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              gap: \"0\",\r\n              maxWidth: \"500px\",\r\n              margin: \"0 auto\"\r\n            }}\r\n          >\r\n            <input\r\n              type=\"email\"\r\n              placeholder=\"Your email address\"\r\n              style={{\r\n                padding: \"1rem 1.5rem\",\r\n                fontSize: \"1rem\",\r\n                border: \"1px solid #d1d5db\",\r\n                borderRadius: \"8px 0 0 8px\",\r\n                outline: \"none\",\r\n                flex: 1,\r\n                background: \"#ffffff\",\r\n                color: \"#374151\",\r\n                minHeight: \"50px\"\r\n              }}\r\n            />\r\n            <button\r\n              style={{\r\n                background: \"#16d1aa\",\r\n                color: \"#ffffff\",\r\n                border: \"none\",\r\n                borderRadius: \"0 8px 8px 0\",\r\n                padding: \"1rem 2rem\",\r\n                fontWeight: 600,\r\n                fontSize: \"1rem\",\r\n                cursor: \"pointer\",\r\n                minHeight: \"50px\",\r\n                transition: \"background 0.2s ease\"\r\n              }}\r\n            >\r\n              Subscribe\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <svg\r\n          width=\"100%\"\r\n          height=\"120\"\r\n          viewBox=\"0 0 1440 120\"\r\n          style={{\r\n            display: \"block\",\r\n            position: \"absolute\",\r\n            bottom: 0,\r\n            left: 0,\r\n            zIndex: 2,\r\n          }}\r\n          preserveAspectRatio=\"none\"\r\n        >\r\n          <path\r\n            fill=\"#0a0b0d\"\r\n            d=\"\r\n              M0,0\r\n              Q720,160 1440,0\r\n              L1440,120\r\n              L0,120\r\n              Z\r\n            \"\r\n          />\r\n        </svg>\r\n      </div>\r\n      <ResourceSection />\r\n      <BlogSection />\r\n      <NewsletterSection />\r\n      <Footer />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,WAAW;IACb;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAuB,MAAM;IAAI;IACzC;QAAE,MAAM;QAAe,MAAM;IAAI;IACjC;QAAE,MAAM;QAAe,MAAM;IAAI;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAgB,MAAM;IAAI;IAClC;QAAE,MAAM;QAAc,MAAM;IAAI;IAChC;QAAE,MAAM;QAAW,MAAM;IAAI;CAChC;AAED,SAAS;IACL,qBACI,gQAAC;QAAI,WAAW,6MAAM,CAAC,MAAM;;0BACzB,gQAAC;gBAAI,WAAW,6MAAM,CAAC,IAAI;0BAAE;;;;;;0BAC7B,gQAAC;gBAAI,WAAW,6MAAM,CAAC,KAAK;0BACvB,SAAS,GAAG,CAAC,CAAC,qBACX,gQAAC;wBAEG,MAAM,KAAK,IAAI;wBACf,WAAW,AAAC,GAAoB,OAAlB,6MAAM,CAAC,OAAO,EAAC,KAA6C,OAA1C,KAAK,IAAI,KAAK,SAAS,6MAAM,CAAC,MAAM,GAAG;kCAEtE,KAAK,IAAI;uBAJL,KAAK,IAAI;;;;;;;;;;0BAQ1B,gQAAC;gBAAO,WAAW,6MAAM,CAAC,YAAY;0BAAE;;;;;;;;;;;;AAGpD;KAlBS;AAoBT,MAAM,aAAa;IACjB;QAAE,MAAM;IAAa;IACrB;QAAE,MAAM;QAAe,KAAK;YAAC;YAAuC;YAAW;YAAa;YAAc;YAAoB;YAAgB;YAAU;YAAiB;YAAiB;YAAwB;YAA4B;YAAmB;YAAiB;YAAiB;YAAU;YAAU;YAAiB;YAA4B;YAAoC;SAA0B;IAAC;IACna;QAAE,MAAM;QAAgB,KAAK;YAAC;YAAmB;YAA2B;YAAkB;YAAW;YAAyB;YAA2B;YAAgB;YAAoB;YAAuB;YAAqB;SAAqB;IAAC;IACnQ;QAAE,MAAM;QAAS,KAAK;YAAC;YAAa;YAAa;YAAQ;YAAW;YAAY;YAAS;YAAU;YAAa;YAAc;YAAS;YAAU;SAAa;IAAC;IAC/J;QAAE,MAAM;QAAgB,KAAK;YAAC;YAAa;YAAmB;YAAe;YAAe;YAAsB;YAAgB;SAAiB;IAAC;IACpJ;QAAE,MAAM;QAAe,KAAK;YAAC;YAAmB;YAAqB;YAAuB;YAAyB;SAAmB;IAAC;IACzI;QAAE,MAAM;QAAY,KAAK;YAAC;YAAmB;YAAoB;YAAwB;YAAmB;YAAgB;YAAoB;YAAiB;YAAkB;SAAoB;IAAC;CACzM;AAWD,MAAM,OAAc;IAClB;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,MAAM;YAAC;YAA4B;SAAY;IACjD;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,MAAM;YAAC;YAAuC;SAAY;IAC5D;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,MAAM;YAAC;YAAmB;SAAY;IACxC;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,MAAM;YAAC;YAAa;SAAY;IAClC;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,MAAM;YAAC;YAAmB;SAAY;IACxC;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,MAAM;QACN,MAAM;YAAC;YAAqB;SAAS;IACvC;CACD;AAED,SAAS;;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,4OAAQ,EAAC;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,4OAAQ,EAAgB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,4OAAQ,EAAgB;IAE9D,MAAM,eACJ,aAAa,aACT,OACA,cACA,KAAK,MAAM,CAAC,CAAC;YAAiC;eAA3B,EAAE,QAAQ,KAAK,cAAY,UAAA,EAAE,IAAI,cAAN,8BAAA,QAAQ,QAAQ,CAAC;SAC/D,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK;IAExC,qBACE,gQAAC;QAAI,WAAW,mNAAc,CAAC,cAAc;;0BAC3C,gQAAC;gBAAM,WAAW,mNAAc,CAAC,OAAO;0BACrC,WAAW,GAAG,CAAC,CAAC,oBACf,gQAAC;;0CACC,gQAAC;gCACC,WAAW,AAAC,GACV,OADY,mNAAc,CAAC,aAAa,EAAC,KAE1C,OADC,aAAa,IAAI,IAAI,GAAG,mNAAc,CAAC,MAAM,GAAG;gCAElD,SAAS;oCACP,YAAY,IAAI,IAAI;oCACpB,eAAe;oCACf,uCAAuC;oCACvC,IAAI,IAAI,IAAI,KAAK,YAAY;wCAC3B,gBAAgB,iBAAiB,IAAI,IAAI,GAAG,OAAO,IAAI,IAAI;oCAC7D;gCACF;gCACA,OAAO;oCACL,OAAO,aAAa,IAAI,IAAI,GAAG,YAAY;oCAC3C,YAAY,aAAa,IAAI,IAAI,GAAG,MAAM;oCAC1C,gBAAgB;oCAChB,OAAO;gCACT;;oCAGC,IAAI,IAAI;oCACR,IAAI,IAAI,KAAK,4BACZ,gQAAC;wCAAK,WAAW,mNAAc,CAAC,YAAY;kDAAG,KAAK,MAAM;;;;;;oCAG3D,IAAI,IAAI,KAAK,4BACZ,gQAAC;wCAAK,OAAO;4CAAE,YAAY;4CAAQ,UAAU;wCAAG;kDAC7C,iBAAiB,IAAI,IAAI,GAAG,MAAM;;;;;;;;;;;;4BAKxC,IAAI,IAAI,KAAK,cAAc,iBAAiB,IAAI,IAAI,kBACnD,gQAAC;gCAAI,WAAW,mNAAc,CAAC,WAAW;0CACvC,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,IAC3B,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,oBACX,gQAAC;wCAEC,WAAW,AAAC,GACV,OADY,mNAAc,CAAC,eAAe,EAAC,KAE5C,OADC,gBAAgB,MAAM,mNAAc,CAAC,MAAM,GAAG;wCAEhD,SAAS;4CACP,YAAY,IAAI,IAAI;4CACpB,eAAe;wCACjB;kDAEC;uCATI;;;;8DAaT,gQAAC;oCACC,WAAW,mNAAc,CAAC,eAAe;oCACzC,OAAO;wCAAE,OAAO;wCAAQ,WAAW;oCAAS;8CAC7C;;;;;;;;;;;;uBAtDC,IAAI,IAAI;;;;;;;;;;0BA+DtB,gQAAC;gBAAI,WAAW,mNAAc,CAAC,IAAI;0BAChC,aAAa,GAAG,CAAC,CAAC,oBACjB,gQAAC;wBAAoB,WAAW,mNAAc,CAAC,IAAI;;0CACjD,gQAAC;gCAAI,WAAW,mNAAc,CAAC,QAAQ;0CAAG,IAAI,IAAI;;;;;;0CAClD,gQAAC;gCAAI,WAAW,mNAAc,CAAC,SAAS;0CAAG,IAAI,KAAK;;;;;;0CACpD,gQAAC;gCAAI,WAAW,mNAAc,CAAC,QAAQ;0CAAG,IAAI,IAAI;;;;;;0CAClD,gQAAC;gCACC,MAAM,IAAI,IAAI;gCACd,WAAW,mNAAc,CAAC,QAAQ;gCAClC,QAAO;gCACP,KAAI;gCACJ,OAAM;0CACP;;;;;;;uBAVO,IAAI,KAAK;;;;;;;;;;;;;;;;AAkB7B;GAnGS;MAAA;AAqGT,SAAS;IACP,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;YACV,OAAO;YACP,aAAa;YACb,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,gQAAC;QAAQ,OAAO;YACd,YAAY;YACZ,SAAS;YACT,OAAO;QACT;kBACE,cAAA,gQAAC;YAAI,OAAO;gBACV,UAAU;gBACV,QAAQ;YACV;;8BACE,gQAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,YAAY;wBACZ,gBAAgB;wBAChB,cAAc;oBAChB;;sCACE,gQAAC;4BAAG,OAAO;gCACT,UAAU;gCACV,YAAY;gCACZ,OAAO;gCACP,QAAQ;4BACV;sCAAG;;;;;;sCAGH,gQAAC;4BAAE,MAAK;4BAAI,OAAO;gCACjB,OAAO;gCACP,gBAAgB;gCAChB,UAAU;gCACV,YAAY;gCACZ,SAAS;gCACT,YAAY;gCACZ,KAAK;4BACP;sCAAG;;;;;;;;;;;;8BAKL,gQAAC;oBAAI,OAAO;wBACV,SAAS;wBACT,qBAAqB;wBACrB,KAAK;oBACP;8BACG,UAAU,GAAG,CAAC,CAAC,qBACd,gQAAC;4BAAsB,OAAO;gCAC5B,YAAY;gCACZ,cAAc;gCACd,UAAU;gCACV,QAAQ;gCACR,YAAY;gCACZ,QAAQ;4BACV;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BACpC;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;gCAClC,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BACpC;;8CACE,gQAAC;oCAAI,OAAO;wCACV,QAAQ;wCACR,YAAY,KAAK,QAAQ;wCACzB,UAAU;wCACV,SAAS;wCACT,YAAY;wCACZ,gBAAgB;oCAClB;;sDACE,gQAAC;4CAAI,OAAO;gDACV,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,cAAc;gDACd,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,gBAAgB;4CAClB;sDACE,cAAA,gQAAC;gDAAK,OAAO;oDACX,UAAU;oDACV,OAAO;gDACT;0DACG,KAAK,QAAQ,KAAK,eAAe,OACjC,KAAK,QAAQ,KAAK,aAAa,MAAM;;;;;;;;;;;sDAG1C,gQAAC;4CAAK,OAAO;gDACX,UAAU;gDACV,KAAK;gDACL,MAAM;gDACN,YAAY;gDACZ,OAAO;gDACP,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,gBAAgB;4CAClB;sDACG,KAAK,QAAQ;;;;;;;;;;;;8CAIlB,gQAAC;oCAAI,OAAO;wCACV,SAAS;oCACX;;sDACE,gQAAC;4CAAG,OAAO;gDACT,UAAU;gDACV,YAAY;gDACZ,OAAO;gDACP,cAAc;gDACd,YAAY;4CACd;sDACG,KAAK,KAAK;;;;;;sDAGb,gQAAC;4CAAE,OAAO;gDACR,OAAO;gDACP,YAAY;gDACZ,cAAc;gDACd,UAAU;4CACZ;sDACG,KAAK,WAAW;;;;;;sDAGnB,gQAAC;4CAAI,OAAO;gDACV,SAAS;gDACT,YAAY;gDACZ,KAAK;4CACP;;8DACE,gQAAC;oDAAI,OAAO;wDACV,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY;wDACZ,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,OAAO;wDACP,YAAY;wDACZ,UAAU;oDACZ;8DACG,KAAK,MAAM,CAAC,MAAM,CAAC;;;;;;8DAEtB,gQAAC;;sEACC,gQAAC;4DAAI,OAAO;gEACV,OAAO;gEACP,YAAY;gEACZ,UAAU;4DACZ;sEACG,KAAK,MAAM;;;;;;sEAEd,gQAAC;4DAAI,OAAO;gEACV,OAAO;gEACP,UAAU;4DACZ;;gEACG,KAAK,IAAI;gEAAC;gEAAI,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/GxB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AA0HjC;MA1MS;AA4MT,SAAS;IACP,qBACE,gQAAC;QAAO,OAAO;YACb,YAAY;YACZ,SAAS;YACT,OAAO;YACP,WAAW;QACb;kBACE,cAAA,gQAAC;YAAI,OAAO;gBACV,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,qBAAqB;gBACrB,KAAK;YACP;;8BAEE,gQAAC;;sCACC,gQAAC;4BAAI,OAAO;gCACV,OAAO;gCACP,UAAU;gCACV,YAAY;gCACZ,QAAQ;gCACR,SAAS;gCACT,SAAS;gCACT,cAAc;gCACd,cAAc;gCACd,YAAY;4BACd;sCAAG;;;;;;sCAGH,gQAAC;4BAAE,OAAO;gCACR,OAAO;gCACP,cAAc;gCACd,YAAY;4BACd;sCAAG;;;;;;sCAGH,gQAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;4BAAS;;8CAC3C,gQAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;wCACL,SAAS;wCACT,QAAQ;wCACR,cAAc;wCACd,YAAY;wCACZ,OAAO;wCACP,MAAM;wCACN,SAAS;oCACX;;;;;;8CAEF,gQAAC;oCAAO,OAAO;wCACb,YAAY;wCACZ,OAAO;wCACP,QAAQ;wCACR,cAAc;wCACd,SAAS;wCACT,YAAY;wCACZ,QAAQ;wCACR,WAAW;oCACb;8CAAG;;;;;;;;;;;;;;;;;;8BAOP,gQAAC;;sCACC,gQAAC;4BAAG,OAAO;gCACT,OAAO;gCACP,cAAc;gCACd,UAAU;4BACZ;sCAAG;;;;;;sCAGH,gQAAC;4BAAG,OAAO;gCACT,WAAW;gCACX,SAAS;gCACT,QAAQ;4BACV;sCACG;gCACC;gCAAQ;gCAAuB;gCAAe;gCAAe;gCAC7D;gCAAgB;gCAAc;gCAAW;gCAAS;gCAAW;6BAC9D,CAAC,GAAG,CAAC,CAAC,qBACL,gQAAC;oCAAc,OAAO;wCAAE,cAAc;oCAAU;8CAC9C,cAAA,gQAAC;wCAAE,MAAK;wCAAI,OAAO;4CACjB,OAAO;4CACP,gBAAgB;4CAChB,YAAY;wCACd;kDACG;;;;;;mCANI;;;;;;;;;;;;;;;;8BAcf,gQAAC;;sCACC,gQAAC;4BAAG,OAAO;gCACT,OAAO;gCACP,cAAc;gCACd,UAAU;4BACZ;sCAAG;;;;;;sCAGH,gQAAC;4BAAG,OAAO;gCACT,WAAW;gCACX,SAAS;gCACT,QAAQ;4BACV;sCACG;gCACC;gCAAgC;gCAA4B;gCAAc;gCAAW;gCACrF;gCAAgB;gCAAiB;gCAAU;gCAC3C;gCAA4B;6BAC7B,CAAC,GAAG,CAAC,CAAC,qBACL,gQAAC;oCAAc,OAAO;wCAAE,cAAc;oCAAU;8CAC9C,cAAA,gQAAC;wCAAE,MAAK;wCAAI,OAAO;4CACjB,OAAO;4CACP,gBAAgB;4CAChB,YAAY;wCACd;kDACG;;;;;;mCANI;;;;;;;;;;;;;;;;8BAcf,gQAAC;;sCACC,gQAAC;4BAAG,OAAO;gCACT,OAAO;gCACP,cAAc;gCACd,UAAU;4BACZ;sCAAG;;;;;;sCAGH,gQAAC;4BAAG,OAAO;gCACT,WAAW;gCACX,SAAS;gCACT,QAAQ;4BACV;sCACG;gCACC;oCAAE,MAAM;oCAAa,MAAM;gCAAK;gCAChC;oCAAE,MAAM;oCAAa,MAAM;gCAAK;gCAChC;oCAAE,MAAM;oCAAQ,MAAM;gCAAK;gCAC3B;oCAAE,MAAM;oCAAW,MAAM;gCAAI;gCAC7B;oCAAE,MAAM;oCAAY,MAAM;gCAAK;gCAC/B;oCAAE,MAAM;oCAAS,MAAM;gCAAK;gCAC5B;oCAAE,MAAM;oCAAU,MAAM;gCAAK;gCAC7B;oCAAE,MAAM;oCAAa,MAAM;gCAAK;gCAChC;oCAAE,MAAM;oCAAc,MAAM;gCAAK;gCACjC;oCAAE,MAAM;oCAAS,MAAM;gCAAI;6BAC5B,CAAC,GAAG,CAAC,CAAC,qBACL,gQAAC;oCAAmB,OAAO;wCAAE,cAAc;oCAAU;8CACnD,cAAA,gQAAC;wCAAE,MAAK;wCAAI,OAAO;4CACjB,OAAO;4CACP,gBAAgB;4CAChB,SAAS;4CACT,YAAY;4CACZ,KAAK;wCACP;;0DACE,gQAAC;0DAAM,KAAK,IAAI;;;;;;4CACf,KAAK,IAAI;;;;;;;mCATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBhC;MA7KS;AA+KT,SAAS;IACP,qBACE,gQAAC;QAAQ,OAAO;YACd,UAAU;YACV,YAAY;YACZ,SAAS;YACT,UAAU;QACZ;;0BAEE,gQAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,SAAS;gBACX;;kCAEE,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;kCAGA,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;kCAGA,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;kCAEA,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;kCAEA,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;kCAGA,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;kCAEA,gQAAC;wBAAI,OAAO;4BACV,UAAU;4BACV,KAAK;4BACL,OAAO;4BACP,OAAO;4BACP,QAAQ;4BACR,YAAY;4BACZ,WAAW;4BACX,cAAc;4BACd,QAAQ;wBACV;;;;;;;;;;;;0BAIF,gQAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,QAAQ;oBACR,WAAW;gBACb;;kCACE,gQAAC;wBAAG,OAAO;4BACT,OAAO;4BACP,YAAY;4BACZ,UAAU;4BACV,cAAc;4BACd,YAAY;4BACZ,YAAY;wBACd;kCAAG;;;;;;kCAIH,gQAAC;wBAAE,OAAO;4BACR,OAAO;4BACP,UAAU;4BACV,cAAc;4BACd,YAAY;wBACd;kCAAG;;;;;;kCAKH,gQAAC;wBAAI,OAAO;4BACV,SAAS;4BACT,gBAAgB;4BAChB,KAAK;4BACL,UAAU;4BACV,QAAQ;wBACV;;0CACE,gQAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;oCACL,SAAS;oCACT,UAAU;oCACV,cAAc;oCACd,SAAS;oCACT,MAAM;oCACN,YAAY;oCACZ,OAAO;oCACP,gBAAgB;oCAChB,QAAQ;oCACR,WAAW;gCACb;;;;;;0CAEF,gQAAC;gCAAO,OAAO;oCACb,YAAY;oCACZ,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,SAAS;oCACT,YAAY;oCACZ,UAAU;oCACV,QAAQ;oCACR,YAAY;oCACZ,eAAe;oCACf,eAAe;oCACf,WAAW;gCACb;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOb;MAlLS;AAoLM,SAAS;IACtB,qBACE,gQAAC;QACC,OAAO;YACL,WAAW;YACX,OAAO;YACP,YAAY;QACd;;0BAEA,gQAAC;gBAAI,WAAW,6MAAM,CAAC,WAAW;;kCAChC,gQAAC;;;;;kCACD,gQAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAa,WAAW;4BAAU,UAAU;4BAAS,QAAQ;wBAAS;;0CAC3F,gQAAC;gCAAG,OAAO;oCAAE,OAAO;oCAAW,YAAY;oCAAK,UAAU;oCAAQ,YAAY;oCAAO,cAAc;gCAAS;;oCAAG;kDACvG,gQAAC;wCAAK,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;oCAAqC;;;;;;;0CAEhF,gQAAC;gCAAE,OAAO;oCAAE,OAAO;oCAAW,YAAY;oCAAc,UAAU;oCAAW,QAAQ;oCAAc,YAAY;gCAAM;0CAAG;;;;;;0CAGxH,gQAAC;gCACC,OAAO;oCACL,SAAS;oCACT,gBAAgB;oCAChB,KAAK;oCACL,UAAU;oCACV,QAAQ;gCACV;;kDAEA,gQAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;4CACL,SAAS;4CACT,UAAU;4CACV,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,MAAM;4CACN,YAAY;4CACZ,OAAO;4CACP,WAAW;wCACb;;;;;;kDAEF,gQAAC;wCACC,OAAO;4CACL,YAAY;4CACZ,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,SAAS;4CACT,YAAY;4CACZ,UAAU;4CACV,QAAQ;4CACR,WAAW;4CACX,YAAY;wCACd;kDACD;;;;;;;;;;;;;;;;;;kCAKL,gQAAC;wBACC,OAAM;wBACN,QAAO;wBACP,SAAQ;wBACR,OAAO;4BACL,SAAS;4BACT,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,QAAQ;wBACV;wBACA,qBAAoB;kCAEpB,cAAA,gQAAC;4BACC,MAAK;4BACL,GAAE;;;;;;;;;;;;;;;;;0BAUR,gQAAC;;;;;0BACD,gQAAC;;;;;0BACD,gQAAC;;;;;0BACD,gQAAC;;;;;;;;;;;AAGP;MA3FwB", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(type, key, props, owner, debugStack, debugTask) {\n      var refProp = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== refProp ? refProp : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        maybeKey,\n        getOwner(),\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (type, config, maybeKey, isStaticChildren) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS;QAClE,IAAI,UAAU,MAAM,GAAG;QACvB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,UAAU,UAAU,IAAI,IACzC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,UACA,YACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,qKACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SAAU,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACjE,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/second-hq/second-hq-frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}