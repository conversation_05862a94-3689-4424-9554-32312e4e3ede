/* [project]/Documents/GitHub/second-hq/second-hq-frontend/app/ResourceSection.module.css [app-client] (css) */
.ResourceSection-module__fanLqG__sectionWrapper {
  background: #f9fafb;
  gap: 3rem;
  width: 100%;
  max-width: 1320px;
  min-height: 90vh;
  margin: 0 auto;
  padding: 3rem 2rem;
  display: flex;
}

.ResourceSection-module__fanLqG__sidebar {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  flex-direction: column;
  gap: .5rem;
  min-width: 280px;
  height: -moz-fit-content;
  height: fit-content;
  padding: 1.5rem;
  display: flex;
  position: -webkit-sticky;
  position: sticky;
  top: 6rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
}

.ResourceSection-module__fanLqG__sidebarButton {
  color: #6b7280;
  text-align: left;
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 6px;
  align-items: center;
  gap: .75rem;
  padding: .75rem 1rem;
  font-size: .9rem;
  font-weight: 500;
  transition: all .2s;
  display: flex;
  position: relative;
}

.ResourceSection-module__fanLqG__sidebarButton.ResourceSection-module__fanLqG__active, .ResourceSection-module__fanLqG__sidebarButton:hover {
  color: #16d1aa;
  background: #f3f4f6;
  transform: none;
}

.ResourceSection-module__fanLqG__sidebarCount {
  color: #fff;
  background: #16d1aa;
  border-radius: 6px;
  margin-left: auto;
  padding: .25rem .5rem;
  font-size: .75rem;
  font-weight: 600;
}

.ResourceSection-module__fanLqG__sidebarCategory {
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  align-items: center;
  gap: .75rem;
  padding: .75rem 1rem;
  font-size: .9rem;
  transition: all .2s;
  display: flex;
}

.ResourceSection-module__fanLqG__sidebarCategory:hover {
  color: #16d1aa;
  background: #f3f4f6;
}

.ResourceSection-module__fanLqG__grid {
  flex: 1;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  display: grid;
}

.ResourceSection-module__fanLqG__card {
  color: #374151;
  cursor: pointer;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  min-height: 200px;
  padding: 1.5rem;
  transition: all .2s;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, .1);
}

.ResourceSection-module__fanLqG__card:hover {
  border: 1px solid #16d1aa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(22, 209, 170, .15);
}

.ResourceSection-module__fanLqG__cardIcon {
  color: #374151;
  background: #f3f4f6;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  display: flex;
}

.ResourceSection-module__fanLqG__cardTitle {
  color: #111827;
  margin-bottom: .75rem;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
}

.ResourceSection-module__fanLqG__cardDesc {
  color: #6b7280;
  margin-bottom: 1rem;
  font-size: .95rem;
  line-height: 1.6;
}

.ResourceSection-module__fanLqG__cardLink {
  color: #9ca3af;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all .2s;
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
}

.ResourceSection-module__fanLqG__cardLink:hover {
  color: #16d1aa;
  transform: none;
}

.ResourceSection-module__fanLqG__categoryBadge {
  color: #6b7280;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-top: .5rem;
  padding: .25rem .75rem;
  font-size: .75rem;
  font-weight: 500;
  display: inline-block;
}

@media (max-width: 1024px) {
  .ResourceSection-module__fanLqG__sectionWrapper {
    flex-direction: column;
    gap: 2rem;
    max-width: 100%;
    padding: 2rem 1.5rem;
  }

  .ResourceSection-module__fanLqG__sidebar {
    min-width: unset;
    padding: 1.5rem;
    position: static;
  }

  .ResourceSection-module__fanLqG__grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .ResourceSection-module__fanLqG__sectionWrapper {
    gap: 1.5rem;
    padding: 1.5rem 1rem;
  }

  .ResourceSection-module__fanLqG__sidebar {
    padding: 1rem;
  }

  .ResourceSection-module__fanLqG__grid {
    grid-template-columns: 1fr;
  }

  .ResourceSection-module__fanLqG__card {
    min-height: 180px;
    padding: 1.5rem;
  }
}

@keyframes ResourceSection-module__fanLqG__fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ResourceSection-module__fanLqG__card {
  animation: .6s forwards ResourceSection-module__fanLqG__fadeInUp;
}

.ResourceSection-module__fanLqG__card:first-child {
  animation-delay: .1s;
}

.ResourceSection-module__fanLqG__card:nth-child(2) {
  animation-delay: .2s;
}

.ResourceSection-module__fanLqG__card:nth-child(3) {
  animation-delay: .3s;
}

.ResourceSection-module__fanLqG__card:nth-child(4) {
  animation-delay: .4s;
}

.ResourceSection-module__fanLqG__card:nth-child(5) {
  animation-delay: .5s;
}

.ResourceSection-module__fanLqG__card:nth-child(6) {
  animation-delay: .6s;
}

.ResourceSection-module__fanLqG__subDropdown {
  border-left: 2px solid rgba(29, 233, 182, .2);
  margin-top: .5rem;
  margin-left: 1rem;
  padding-left: 1rem;
}

.ResourceSection-module__fanLqG__subDropdownItem {
  color: #94a3b8;
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: .25rem;
  padding: .75rem 1rem;
  font-size: .9rem;
  transition: all .2s;
}

.ResourceSection-module__fanLqG__subDropdownItem:hover, .ResourceSection-module__fanLqG__subDropdownItem.ResourceSection-module__fanLqG__active {
  color: #1de9b6;
  background: rgba(29, 233, 182, .1);
}

/*# sourceMappingURL=80b94_GitHub_second-hq_second-hq-frontend_app_ResourceSection_module_css_bad6b30c._.single.css.map*/