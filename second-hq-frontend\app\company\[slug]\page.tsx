import styles from "./CompanyPage.module.css";

// Generate static params for all company slugs
export async function generateStaticParams() {
  return [
    { slug: 'microsoft-india' },
    { slug: 'goldman-sachs-bengaluru' },
  ];
}

interface PageProps {
  params: {
    slug: string;
  };
}

// Sample company data - in real app this would come from API/database
const companyData = {
  "microsoft-india": {
    name: "Microsoft India Development Center",
    logo: "🏢",
    description: "One of Microsoft's largest R&D centers outside the US, focusing on cloud, AI, and enterprise solutions with over 8,000 employees.",
    industry: "Technology & IT Services",
    location: "Hyderabad",
    website: "https://microsoft.com",
    
    // First-order Snapshot
    firstOrder: {
      cityBase: "Bengaluru",
      industryServed: "FinTech & SaaS", 
      talentSize: "~1,600 employees in India",
      aiReadinessScore: "High (AI integrated across product, data, and hiring initiatives)",
      xFactor: '"Builder culture" — engineers own full-stack products, not just features',
      cultureSignal: "High Glassdoor score, strong EVP focus on innovation + inclusion"
    },

    // Second-order Snapshot  
    secondOrder: {
      capabilitiesOwned: "Global product engineering for QuickBooks, TurboTax; data science hub; CX/UX research",
      parentCompanyRank: "Fortune 500, Nasdaq-listed",
      techStack: "AI/ML, cloud-native, design systems, full-stack engineering",
      secondHQMandate: "Not a back-office; end-to-end product ownership, India-first pilots",
      esgDeiSignals: "30% women in tech roles (above industry average)",
      leadershipDepth: "India site lead is part of Intuit's global tech council."
    },

    // Key Metrics
    metrics: {
      totalEmployees: "8,000+",
      techEmployees: "6,500+",
      founded: "1998",
      revenue: "$198B (Parent)",
      funding: "Public Company",
      glassdoorRating: "4.4/5"
    }
  }
};

export default function CompanyPage({ params }: PageProps) {
  const slug = params.slug;
  const company = companyData[slug as keyof typeof companyData];

  if (!company) {
    return <div>Company not found</div>;
  }

  return (
    <div className={styles.pageContainer}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.companyInfo}>
            <div className={styles.logoContainer}>
              <span className={styles.logo}>{company.logo}</span>
            </div>
            <div className={styles.companyDetails}>
              <h1 className={styles.companyName}>{company.name}</h1>
              <p className={styles.companyDescription}>{company.description}</p>
              <div className={styles.tags}>
                <span className={styles.tag}>{company.industry}</span>
                <span className={styles.tag}>{company.location}</span>
              </div>
            </div>
          </div>
          <div className={styles.headerActions}>
            <button className={styles.visitButton}>
              Visit Website ↗
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={styles.mainContent}>
        <div className={styles.layoutContainer}>

          {/* Sidebar */}
          <aside className={styles.sidebar}>
            <div className={styles.sidebarContent}>
              <h3 className={styles.sidebarTitle}>Quick Info</h3>
              <div className={styles.sidebarItem}>
                <span className={styles.sidebarLabel}>Location</span>
                <span className={styles.sidebarValue}>{company.location}</span>
              </div>
              <div className={styles.sidebarItem}>
                <span className={styles.sidebarLabel}>Industry</span>
                <span className={styles.sidebarValue}>{company.industry}</span>
              </div>
              <div className={styles.sidebarItem}>
                <span className={styles.sidebarLabel}>Website</span>
                <a href={company.website} target="_blank" rel="noopener noreferrer" className={styles.sidebarLink}>
                  Visit Site ↗
                </a>
              </div>
              <div className={styles.sidebarItem}>
                <span className={styles.sidebarLabel}>Employees</span>
                <span className={styles.sidebarValue}>{company.metrics.totalEmployees}</span>
              </div>
              <div className={styles.sidebarItem}>
                <span className={styles.sidebarLabel}>Founded</span>
                <span className={styles.sidebarValue}>{company.metrics.founded}</span>
              </div>
              <div className={styles.sidebarItem}>
                <span className={styles.sidebarLabel}>Rating</span>
                <span className={styles.sidebarValue}>{company.metrics.glassdoorRating}</span>
              </div>
            </div>
          </aside>

          {/* Main Content Area */}
          <div className={styles.contentArea}>

            {/* First-order Snapshot (Top) */}
            <section className={styles.snapshotCard}>
              <h2 className={styles.sectionTitle}>First-order Snapshot</h2>
              <div className={styles.snapshotContent}>
                <div className={styles.snapshotItem}>
                  <strong>City Base:</strong> {company.firstOrder.cityBase}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Industry Served:</strong> {company.firstOrder.industryServed}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Talent Size:</strong> {company.firstOrder.talentSize}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>AI Readiness Score:</strong> {company.firstOrder.aiReadinessScore}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>X-Factor:</strong> {company.firstOrder.xFactor}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Culture Signal:</strong> {company.firstOrder.cultureSignal}
                </div>
              </div>
            </section>

            
            {/* Second-order Snapshot */}
            <section className={styles.snapshotCard}>
              <h2 className={styles.sectionTitle}>Second-order Snapshot</h2>
            
              <div className={styles.snapshotContent}>
                <div className={styles.snapshotItem}>
                  <strong>Capabilities Owned:</strong> {company.secondOrder.capabilitiesOwned}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Parent Company Rank:</strong> {company.secondOrder.parentCompanyRank}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Tech Stack:</strong> {company.secondOrder.techStack}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Second HQ Mandate:</strong> {company.secondOrder.secondHQMandate}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>ESG & DEI Signals:</strong> {company.secondOrder.esgDeiSignals}
                </div>
                <div className={styles.snapshotItem}>
                  <strong>Leadership Depth:</strong> {company.secondOrder.leadershipDepth}
                </div>
              </div>
            </section>

 {/* About Section (Last) */}
            <section className={styles.aboutSection}>
              <h2 className={styles.sectionTitle}>About First-order Snapshot</h2>
              <div className={styles.aboutContent}>
                <p>
                  {company.name} represents one of the most significant technology and innovation hubs in India,
                  serving as a cornerstone of Microsoft's global operations. Established in 1998, this development
                  center has grown to become one of the largest R&D facilities outside the United States, housing
                  over 8,000 talented professionals across multiple cities in India.
                </p>
                <p>
                  The center specializes in cutting-edge research and development across cloud computing, artificial
                  intelligence, machine learning, and enterprise software solutions. Teams here work on products and
                  services that impact millions of users worldwide, contributing significantly to Microsoft's core
                  offerings including Azure, Office 365, Windows, and emerging AI technologies.
                </p>
                <p>
                  Beyond product development, the center plays a crucial role in Microsoft's commitment to digital
                  transformation in India. It actively supports the local tech ecosystem through partnerships with
                  startups, educational institutions, and government initiatives, while maintaining Microsoft's
                  high standards for innovation, inclusion, and sustainable technology practices.
                </p>
              </div>
            </section>


             {/* About Section (Last) */}
            {/* <section className={styles.aboutSection}>
              <h2 className={styles.sectionTitle}>About Second-order Snapshot</h2>
              <div className={styles.aboutContent}>
                <p>
                  The second-order analysis reveals {company.name}'s strategic positioning within the global technology
                  ecosystem and its operational excellence in India. This development center demonstrates sophisticated
                  capabilities in end-to-end product ownership, advanced technology implementation, and leadership
                  development that extends far beyond traditional offshore models.
                </p>
                <p>
                  The center specializes in cutting-edge research and development across cloud computing, artificial
                  intelligence, machine learning, and enterprise software solutions. Teams here work on products and
                  services that impact millions of users worldwide, contributing significantly to Microsoft's core
                  offerings including Azure, Office 365, Windows, and emerging AI technologies.
                </p>
                <p>
                  With comprehensive ownership of critical global products and a mandate that emphasizes innovation
                  over cost arbitrage, the center showcases Microsoft's commitment to India as a strategic technology
                  hub. The facility's advanced tech stack, strong ESG initiatives, and deep leadership bench reflect
                  a mature organization capable of driving global technology transformation from India.
                </p>
              </div>
            </section> */}

          </div>
        </div>
      </main>
    </div>
  );
}
